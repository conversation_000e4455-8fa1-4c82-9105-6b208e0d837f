{
  "compilerOptions": {
    "lib": [
      "dom",
      "dom.iterable",
      "esnext",
      "webworker"
    ],
    "baseUrl": ".",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "noImplicitAny": true,
    "target": "ES2020",
    "jsx": "react-jsx",
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "sourceMap": true,        
    "paths": {
      "src/*": ["src/*"],
      "@/*": ["./*"], 
      "@/utils": ["src/background/util"],

    }
  },

  "include": [
    "src",
    "config",
    "components"
  ],
  "exclude": [
    "test",
    "jest.config.js",
    "node_modules",
    "dist"
  ]
}
