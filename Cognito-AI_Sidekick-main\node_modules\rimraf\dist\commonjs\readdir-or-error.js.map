{"version": 3, "file": "readdir-or-error.js", "sourceRoot": "", "sources": ["../../src/readdir-or-error.ts"], "names": [], "mappings": ";;;AAAA,kDAAkD;AAClD,6CAA6C;AAC7C,mCAA+C;AAC/C,MAAM,EAAE,OAAO,EAAE,GAAG,gBAAQ,CAAA;AACrB,MAAM,cAAc,GAAG,CAAC,IAAY,EAAE,EAAE,CAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAA2B,CAAC,CAAA;AAD3C,QAAA,cAAc,kBAC6B;AACjD,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,EAAE;IACjD,IAAI,CAAC;QACH,OAAO,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,OAAO,EAA2B,CAAA;IACpC,CAAC;AACH,CAAC,CAAA;AANY,QAAA,kBAAkB,sBAM9B", "sourcesContent": ["// returns an array of entries if readdir() works,\n// or the error that readdir() raised if not.\nimport { promises, readdirSync } from './fs.js'\nconst { readdir } = promises\nexport const readdirOrError = (path: string) =>\n  readdir(path).catch(er => er as NodeJS.ErrnoException)\nexport const readdirOrErrorSync = (path: string) => {\n  try {\n    return readdirSync(path)\n  } catch (er) {\n    return er as NodeJS.ErrnoException\n  }\n}\n"]}