{"version": 3, "file": "retry-busy.js", "sourceRoot": "", "sources": ["../../src/retry-busy.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAIrE,MAAM,CAAC,MAAM,UAAU,GAAG,GAAG,CAAA;AAC7B,MAAM,CAAC,MAAM,IAAI,GAAG,GAAG,CAAA;AACvB,MAAM,CAAC,MAAM,UAAU,GAAG,EAAE,CAAA;AAC5B,MAAM,CAAC,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;AAE3D,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,EAAkC,EAAE,EAAE;IAC9D,MAAM,MAAM,GAAG,KAAK,EAClB,IAAY,EACZ,GAAuB,EACvB,OAAO,GAAG,CAAC,EACX,KAAK,GAAG,CAAC,EACT,EAAE;QACF,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,UAAU,CAAA;QACxC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,IAAI,IAAI,CAAA;QAChC,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,UAAU,CAAA;QACxC,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC;gBACH,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAA;YACvB,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC;gBACZ,MAAM,GAAG,GAAG,EAA2B,CAAA;gBACvC,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3D,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;oBACnC,KAAK,GAAG,OAAO,GAAG,KAAK,CAAA;oBACvB,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;wBAChB,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;4BAC9B,UAAU,CAAC,GAAG,EAAE;gCACd,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;4BAClD,CAAC,EAAE,OAAO,CAAC,CAAA;wBACb,CAAC,CAAC,CAAA;oBACJ,CAAC;oBACD,IAAI,OAAO,GAAG,GAAG,EAAE,CAAC;wBAClB,OAAO,EAAE,CAAA;wBACT,SAAQ;oBACV,CAAC;gBACH,CAAC;gBACD,MAAM,EAAE,CAAA;YACV,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IAED,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,uCAAuC;AACvC,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,EAAyB,EAAE,EAAE;IACzD,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,GAAkB,EAAE,EAAE;QAClD,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,UAAU,CAAA;QACxC,IAAI,OAAO,GAAG,CAAC,CAAA;QACf,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC;gBACH,OAAO,EAAE,CAAC,IAAI,CAAC,CAAA;YACjB,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC;gBACZ,MAAM,GAAG,GAAG,EAA2B,CAAA;gBACvC,IACE,GAAG,EAAE,IAAI,KAAK,IAAI;oBAClB,GAAG,EAAE,IAAI;oBACT,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;oBACnB,OAAO,GAAG,GAAG,EACb,CAAC;oBACD,OAAO,EAAE,CAAA;oBACT,SAAQ;gBACV,CAAC;gBACD,MAAM,EAAE,CAAA;YACV,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IACD,OAAO,MAAM,CAAA;AACf,CAAC,CAAA", "sourcesContent": ["// note: max backoff is the maximum that any *single* backoff will do\n\nimport { RimrafAsyncOptions, RimrafOptions } from './index.js'\n\nexport const MAXBACKOFF = 200\nexport const RATE = 1.2\nexport const MAXRETRIES = 10\nexport const codes = new Set(['EMFILE', 'ENFILE', 'EBUSY'])\n\nexport const retryBusy = (fn: (path: string) => Promise<any>) => {\n  const method = async (\n    path: string,\n    opt: RimrafAsyncOptions,\n    backoff = 1,\n    total = 0,\n  ) => {\n    const mbo = opt.maxBackoff || MAXBACKOFF\n    const rate = opt.backoff || RATE\n    const max = opt.maxRetries || MAXRETRIES\n    let retries = 0\n    while (true) {\n      try {\n        return await fn(path)\n      } catch (er) {\n        const fer = er as NodeJS.ErrnoException\n        if (fer?.path === path && fer?.code && codes.has(fer.code)) {\n          backoff = Math.ceil(backoff * rate)\n          total = backoff + total\n          if (total < mbo) {\n            return new Promise((res, rej) => {\n              setTimeout(() => {\n                method(path, opt, backoff, total).then(res, rej)\n              }, backoff)\n            })\n          }\n          if (retries < max) {\n            retries++\n            continue\n          }\n        }\n        throw er\n      }\n    }\n  }\n\n  return method\n}\n\n// just retries, no async so no backoff\nexport const retryBusySync = (fn: (path: string) => any) => {\n  const method = (path: string, opt: RimrafOptions) => {\n    const max = opt.maxRetries || MAXRETRIES\n    let retries = 0\n    while (true) {\n      try {\n        return fn(path)\n      } catch (er) {\n        const fer = er as NodeJS.ErrnoException\n        if (\n          fer?.path === path &&\n          fer?.code &&\n          codes.has(fer.code) &&\n          retries < max\n        ) {\n          retries++\n          continue\n        }\n        throw er\n      }\n    }\n  }\n  return method\n}\n"]}