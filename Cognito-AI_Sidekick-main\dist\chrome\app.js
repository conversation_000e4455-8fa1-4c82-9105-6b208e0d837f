(()=>{"use strict";var e,t,s,a,n,r,o,i={116:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(4848),n=s(6540),r=s(6948);const o=()=>{const{config:e}=(0,r.UK)(),t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{if(!e?.animatedBackground)return;const s=t.current;if(s){s.innerHTML="";{const a=document.createElement("canvas");s.appendChild(a);const n=a.getContext("2d");if(!n)return;const r=16,o=1.2*r,i=1.2*r;function l(){a.width=window.innerWidth,a.height=window.innerHeight}l(),window.addEventListener("resize",l);const c=["N","ﾐ","ﾋ","𐌊","ｳ","ｼ","ﾅ","𐌔","X","ｻ","ﾜ","ㄘ","𑖖","𑖃","𑖆","𐌈","J","ｱ","ﾎ","ﾃ","M","π","Σ","Y","ｷ","ㄠ","ﾕ","ﾗ","ｾ","ﾈ","Ω","ﾀ","ﾇ","ﾍ","ｦ","ｲ","ｸ","W","𐌙","ﾁ","ﾄ","ﾉ","Δ","ﾔ","ㄖ","ﾙ","ﾚ","王","道","Ж","ﾝ","0","1","2","3","4","5","7","8","9","A","B","Z","*","+","д","Ⱟ","𑗁","T","|","ç","ﾘ","Ѯ"],d=["#15803d","#16a34a","#22c55e","#4ade80"],u="#f0fdf4";let m=Math.floor(a.width/o),h=Math.floor(a.height/i),g=Array(m).fill(0),p=Array(m).fill(null).map((()=>Array(h).fill({char:"",color:""}))),f=Array(m).fill(0).map((()=>Math.floor(2*Math.random())+1)),x=Array(m).fill(0);const v=12;function b(){n.clearRect(0,0,a.width,a.height),n.font=`${r}px monospace`,n.textAlign="center",n.textBaseline="top";for(let e=0;e<m;e++){for(let t=0;t<v;t++){const s=g[e]-t;if(s<0)continue;if(s>=h)continue;let a=p[e][s];a&&a.char&&(n.fillStyle=0===t?u:a.color,n.globalAlpha=.3*(1-t/v),n.fillText(a.char,e*o+o/2,s*i))}if(n.globalAlpha=1,x[e]++,x[e]>=f[e]){const t=c[Math.floor(Math.random()*c.length)],s=d[Math.floor(Math.random()*d.length)];p[e][g[e]]={char:t,color:s},g[e]++,g[e]>=h+v&&(g[e]=0,p[e]=Array(h).fill({char:"",color:""}),f[e]=Math.floor(10*Math.random())+10),x[e]=0}}requestAnimationFrame(b)}b();const w=()=>{l(),m=Math.floor(a.width/o),h=Math.floor(a.height/i),g=Array(m).fill(0)};return window.addEventListener("resize",w),()=>{window.removeEventListener("resize",l),window.removeEventListener("resize",w),s.removeChild(a)}}}}),[e?.animatedBackground]),e?.animatedBackground?(0,a.jsx)("div",{ref:t,style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:-1,pointerEvents:"none",overflow:"hidden"}}):null}},523:(e,t,s)=>{s.d(t,{V:()=>i});var a=s(4848),n=s(5284),r=s(6948),o=s(7520);const i=()=>{const{config:e}=(0,r.UK)(),t=e?.persona||"default",s=o.z[t]||o.z.default,i=(0,n.cn)("flex","items-center","justify-center","h-full","fixed","w-full","top-[10%]","pointer-events-none"),l=(0,n.cn)("fixed","opacity-[0.03]","z-[1]");return(0,a.jsx)("div",{className:i,children:(0,a.jsx)("img",{src:s,alt:"",className:l,style:{zoom:"1.2"}})})}},1979:(e,t,s)=>{s.d(t,{z:()=>k});var a=s(4848),n=s(6540),r=s(2090),o=s(4539),i=s(6532),l=s(9696),c=s(7086),d=s(6250),u=s(37),m=s(6555),h=s(7197),g=s(5284);function p({...e}){return(0,a.jsx)(h.bL,{"data-slot":"hover-card",...e})}function f({...e}){return(0,a.jsx)(h.l9,{"data-slot":"hover-card-trigger",...e})}function x({className:e,align:t="center",sideOffset:s=4,...n}){return(0,a.jsx)(h.ZL,{"data-slot":"hover-card-portal",children:(0,a.jsx)(h.UC,{"data-slot":"hover-card-content",align:t,sideOffset:s,className:(0,g.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}var v=s(888),b=s(2955),w=s(6948),y=s(1319),j=s(1905),N=s(6174),S=s(6508);const C={...S.Af,pre:e=>(0,a.jsx)(S.AC,{...e,wrapperClassName:"my-2",className:(0,g.cn)("bg-[var(--code-bg)] text-[var(--code-text)]",e.className),buttonClassName:"h-7 w-7 text-[var(--text)] hover:bg-[var(--text)]/10"})},k=({triggerOpenCreateModal:e,onModalOpened:t})=>{const[s,h]=(0,n.useState)([]),[S,k]=(0,n.useState)(""),[$,M]=(0,n.useState)(1),[A,E]=(0,n.useState)(null),[_,T]=(0,n.useState)(!1),[z,R]=(0,n.useState)(""),[L,P]=(0,n.useState)(""),[O,D]=(0,n.useState)(""),[I,U]=(0,n.useState)(null),{config:F}=(0,w.UK)(),q=(0,n.useCallback)((async()=>{const e=await(0,b.oK)();h(e)}),[]);(0,n.useEffect)((()=>{q()}),[q]);const W=(0,n.useCallback)((e=>{E(null),R(e?.title||""),P(e?.content||""),D(""),T(!0)}),[]);(0,n.useEffect)((()=>{(async()=>{try{const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});e?.id?(console.log(`[NoteSystemView] Component mounted for tab ${e.id}. Sending SIDE_PANEL_READY signal.`),chrome.runtime.sendMessage({type:"SIDE_PANEL_READY",tabId:e.id},(e=>{chrome.runtime.lastError?console.warn("[NoteSystemView] Could not send ready signal:",chrome.runtime.lastError.message):console.log("[NoteSystemView] Background acknowledged ready signal:",e)}))):console.error("[NoteSystemView] Could not determine the tab ID to send ready signal.")}catch(e){console.error("[NoteSystemView] Error sending ready signal:",e)}})()}),[]),(0,n.useEffect)((()=>{const e=(e,t,s)=>{let a=!1;return"CREATE_NOTE_FROM_PAGE_CONTENT"===e.type&&e.payload?(console.log("[NoteSystemView] Received page data. Storing it in state to trigger auto-save."),U(e.payload),s({status:"PAGE_DATA_QUEUED_FOR_AUTO_SAVE"}),a=!0):"ERROR_OCCURRED"===e.type&&e.payload&&(console.log("[NoteSystemView] Received ERROR_OCCURRED via runtime message."),v.oR.error(String(e.payload)),s({status:"ERROR_DISPLAYED_BY_NOTESYSTEM"}),a=!0),!!a};chrome.runtime.onMessage.addListener(e);const t=chrome.runtime.connect({name:N.A.SidePanelPort});return t.postMessage({type:"init"}),t.onMessage.addListener((e=>{if("ADD_SELECTION_TO_NOTE"===e.type){console.log("[NoteSystemView] Handling ADD_SELECTION_TO_NOTE via port");const t=L?`${L}\n\n${e.payload}`:e.payload;_?P(t):W({content:t,title:"Note with Selection"}),v.oR.success("Selection added to note draft.")}})),()=>{console.log("[NoteSystemView] Cleaning up listeners."),chrome.runtime.onMessage.removeListener(e),t.disconnect()}}),[_,L,W]),(0,n.useEffect)((()=>{(async()=>{if(I){console.log("[NoteSystemView] pendingPageData detected. Attempting automatic save.");const e={...I};if(U(null),!e.content||""===e.content.trim())return void v.oR.error("Cannot save note: Content is empty.");const t={title:e.title.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:e.content,tags:[]};try{await(0,b.s2)(t),v.oR.success("Page added to notes!"),await q()}catch(e){console.error("[NoteSystemView] Error auto-saving note:",e),v.oR.error("Failed to auto-save note.")}}})()}),[I,q]),(0,n.useEffect)((()=>{e&&(W(),t())}),[e,t,W]);const B=(0,n.useMemo)((()=>{if(!S)return s;const e=S.toLowerCase();return s.filter((t=>{const s=t.title.toLowerCase().includes(e),a=t.content.toLowerCase().includes(e),n=t.tags&&t.tags.some((t=>t.toLowerCase().includes(e)));return s||a||n}))}),[s,S]),V=(0,n.useMemo)((()=>{const e=12*($-1);return B.slice(e,e+12)}),[B,$]),H=(0,n.useMemo)((()=>Math.max(1,Math.ceil(B.length/12))),[B]);return(0,a.jsxs)("div",{className:"flex flex-col h-full text-[var(--text)]",children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.p,{type:"text",placeholder:"Search notes (titles & content & tags)...",value:S,onChange:e=>k(e.target.value),className:(0,g.cn)("w-full bg-background text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10 border-none rounded-none")}),(0,a.jsx)(d.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(o.F,{className:"flex-1",children:0===V.length?(0,a.jsx)("p",{className:"text-center text-[var(--muted-foreground)] py-4",children:S?`No notes found for "${S}".`:"No notes yet. Create one!"}):(0,a.jsx)("div",{className:"space-y-0",children:V.map((e=>(0,a.jsx)("div",{className:"px-2 border-b border-[var(--text)]/10 rounded-none hover:shadow-lg transition-shadow w-full",children:(0,a.jsxs)(p,{openDelay:200,closeDelay:100,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(f,{asChild:!0,children:(0,a.jsx)("h3",{className:"font-semibold text-md truncate cursor-pointer hover:underline",children:e.title})}),(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)(m.AM,{children:[(0,a.jsx)(m.Wv,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(u.jbe,{})})}),(0,a.jsxs)(m.hl,{className:"w-30 bg-[var(--popover)] border-[var(--text)]/10 text-[var(--popover-foreground)] mr-1 p-1 space-y-1 shadow-md",children:[(0,a.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>(e=>{const t=e.tags?e.tags.join(", "):"";E(e),R(e.title),P(e.content),D(t),T(!0)})(e),children:[(0,a.jsx)(d.i5t,{className:"mr-2 size-4"}),"Edit"]}),(0,a.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>{let t="---\n";t+=`title: ${e.title}\n`;const s=e.lastUpdatedAt||e.createdAt;if(s){const e=new Date(s).toISOString().split("T")[0];t+=`date: ${e}\n`}e.tags&&e.tags.length>0&&(t+="tags:\n",e.tags.forEach((e=>{t+=`  - ${e.trim()}\n`}))),t+="---\n\n",t+=e.content;const a=document.createElement("a");a.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(t)}`),a.setAttribute("download",`${e.title}.md`),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)},children:[(0,a.jsx)(d.rII,{className:"mr-2 size-4"}),"ObsidianMD"]}),(0,a.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal text-red-500 hover:text-red-500 hover:bg-red-500/10",onClick:()=>(async e=>{await(0,b.VZ)(e),v.oR.success("Note deleted!"),q()})(e.id),children:[(0,a.jsx)(d.ttk,{className:"mr-2 size-4"})," Delete "]})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Last updated: ",new Date(e.lastUpdatedAt).toLocaleDateString()]}),e.tags&&e.tags.length>0?(0,a.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)] truncate max-w-[50%]",children:["Tags: ",e.tags.join(", ")]}):(0,a.jsx)("p",{className:"text-xs text-[var(--muted-foreground)]",children:"No tags"})]}),(0,a.jsx)(x,{className:(0,g.cn)("bg-[var(--popover)] border-[var(--active)] text-[var(--popover-foreground)] markdown-body","w-[80vw] sm:w-[70vw] md:w-[50vw] lg:w-[40vw]","max-w-lg","max-h-[70vh]","overflow-y-auto thin-scrollbar"),side:"top",align:"start",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Date: ",new Date(e.lastUpdatedAt).toLocaleString()]}),(0,a.jsx)("div",{className:"text-sm whitespace-pre-wrap break-words",children:(0,a.jsx)(y.oz,{remarkPlugins:[j.A],components:C,children:e.content})}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{className:"border-t border-[var(--border)] pt-2 mt-2",children:[(0,a.jsx)("p",{className:"text-xs font-semibold text-[var(--text)] mb-1",children:"Tags:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e=>(0,a.jsx)("span",{className:"text-xs bg-[var(--muted)] text-[var(--muted-foreground)] px-2 py-0.5 rounded",children:e},e)))})]})]})})]})},e.id)))})}),H>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 font-['Space_Mono',_monospace]",children:[(0,a.jsx)(r.$,{onClick:()=>M((e=>Math.max(1,e-1))),disabled:1===$,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,a.jsxs)("span",{className:"text-md",children:["Page ",$," of ",H]}),(0,a.jsx)(r.$,{onClick:()=>M((e=>Math.min(H,e+1))),disabled:$===H,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]}),(0,a.jsx)(c.lG,{open:_,onOpenChange:e=>{e?T(!0):(T(!1),E(null),R(""),P(""),D(""))},children:(0,a.jsxs)(c.Cf,{className:(0,g.cn)("bg-[var(--bg)] border-[var(--text)]/10 w-[90vw] max-w-3xl text-[var(--text)] overflow-hidden","flex flex-col max-h-[85vh]","p-6"),children:[(0,a.jsxs)(c.c7,{children:[(0,a.jsx)(c.L3,{children:A?"Edit Note":"Create New Note"}),(0,a.jsx)(c.rr,{className:"text-[var(--text)]/80 pt-1",children:A?"Update the title or content of your note.":"Provide a title (optional) and content for your new note."})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(i.p,{placeholder:"Note Title (optional)",value:z,onChange:e=>R(e.target.value),className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto thin-scrollbar min-h-0",children:(0,a.jsx)(l.T,{placeholder:"Your note content...",value:L,onChange:e=>P(e.target.value),autosize:!0,minRows:5,className:"w-full bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] resize-none overflow-hidden"})}),(0,a.jsx)("div",{children:(0,a.jsx)(i.p,{placeholder:"Tags (comma-separated)",value:O,onChange:e=>{D(e.target.value)},className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})})]}),(0,a.jsxs)(c.Es,{children:[(0,a.jsx)(r.$,{variant:"outline",onClick:()=>{T(!1),E(null)},children:"Cancel"}),(0,a.jsx)(r.$,{onClick:async()=>{if(!L.trim())return void v.oR.error("Note content cannot be empty.");const e=""===O.trim()?[]:O.split(",").map((e=>e.trim())).filter((e=>e.length>0)),t={id:A?.id,title:z.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:L,tags:e};await(0,b.s2)(t),v.oR.success(A?"Note updated!":"Note created!"),await q(),T(!1),E(null),R(""),P(""),D("")},className:"bg-[var(--active)] text-[var(--active-foreground)] hover:bg-[var(--active)]/90",children:A?"Save Changes":"Create Note"})]})]})})]})}},2050:(e,t,s)=>{s.a(e,(async(e,a)=>{try{s.d(t,{A:()=>O});var n=s(4848),r=s(6540),o=s(888),i=s(3790),l=s.n(i),c=s(5066),d=s(1735),u=s(9197),m=s(2090),h=s(3885),g=s(5284),p=s(9853),f=s(8971),x=s(8698),v=s(523),b=s(8473),w=s(6948),y=s(3193),j=s(7334),N=s(8639),S=s(7660),C=s(2353),k=s(5431),$=s(6174),M=s(5095),A=s(1979),E=e([S]);function _(){let e="",t="",s="",a="",n="",r="",o="";try{e=document.title||"";const i=5e6;if(document.body,document.body&&document.body.innerHTML.length>i){console.warn(`[Cognito Bridge] Document body is very large (${document.body.innerHTML.length} chars). Attempting to use a cloned, simplified version for text extraction to improve performance/stability.`);const e=document.body.cloneNode(!0);e.querySelectorAll("script, style, noscript, iframe, embed, object").forEach((e=>e.remove())),t=(e.textContent||"").replace(/\s\s+/g," ").trim(),s=document.body.innerHTML.replace(/\s\s+/g," ")}else document.body?(t=(document.body.innerText||"").replace(/\s\s+/g," ").trim(),s=(document.body.innerHTML||"").replace(/\s\s+/g," ")):console.warn("[Cognito Bridge] document.body is not available.");a=Array.from(document.images).map((e=>e.alt)).filter((e=>e&&e.trim().length>0)).join(". "),n=Array.from(document.querySelectorAll("table")).map((e=>(e.innerText||"").replace(/\s\s+/g," "))).join("\n");const l=document.querySelector('meta[name="description"]');r=l&&l.getAttribute("content")||"";const c=document.querySelector('meta[name="keywords"]');o=c&&c.getAttribute("content")||""}catch(e){console.error("[Cognito Bridge] Error during content extraction:",e);let t="Unknown extraction error";return e instanceof Error?t=e.message:"string"==typeof e&&(t=e),JSON.stringify({error:`Extraction failed: ${t}`,title:document.title||"Error extracting title",text:"",html:"",altTexts:"",tableData:"",meta:{description:"",keywords:""}})}const i=1e7;let l={title:e,text:t,html:s,altTexts:a,tableData:n,meta:{description:r,keywords:o}};if(JSON.stringify(l).length>i){console.warn("[Cognito Bridge] Total extracted content is very large. Attempting to truncate.");const e=i-JSON.stringify({...l,text:"",html:""}).length;let t=e;l.text.length>.6*t&&(l.text=l.text.substring(0,Math.floor(.6*t))+"... (truncated)"),t=e-l.text.length,l.html.length>.8*t&&(l.html=l.html.substring(0,Math.floor(.8*t))+"... (truncated)"),console.warn("[Cognito Bridge] Content truncated. Final approx length:",JSON.stringify(l).length)}return JSON.stringify(l)}async function T(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(!e?.id||e.url?.startsWith("chrome://")||e.url?.startsWith("chrome-extension://")||e.url?.startsWith("about:"))return k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),void k.A.deleteItem("tabledata");k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata");try{const t=await chrome.scripting.executeScript({target:{tabId:e.id},func:_});if(!t||!Array.isArray(t)||0===t.length||!t[0]||"string"!=typeof t[0].result)return void console.error("[Cognito:] Bridge function execution returned invalid or unexpected results structure:",t);const s=t[0].result;let a;try{a=JSON.parse(s)}catch(e){return void console.error("[Cognito:] Failed to parse JSON result from bridge:",e,"Raw result string:",s)}if(a.error)return void console.error("[Cognito:] Bridge function reported an error:",a.error,"Title:",a.title);try{k.A.setItem("pagestring",a?.text??""),k.A.setItem("pagehtml",a?.html??""),k.A.setItem("alttexts",a?.altTexts??""),k.A.setItem("tabledata",a?.tableData??"")}catch(e){console.error("[Cognito:] Storage error after successful extraction:",e),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")}}catch(e){console.error("[Cognito:] Bridge function execution failed:",e),e instanceof Error&&(e.message.includes('Cannot access contents of url "chrome://')||e.message.includes("Cannot access a chrome extension URL")||e.message.includes('Cannot access contents of url "about:'))&&console.warn("[Cognito:] Cannot access restricted URL.")}}S=(E.then?(await E)():E)[0];const z=()=>`chat_${Math.random().toString(16).slice(2)}`,R=({children:e,onClick:t})=>(0,n.jsx)("div",{className:(0,g.cn)("bg-[var(--active)] border border-[var(--text)] rounded-[16px] text-[var(--text)]","cursor-pointer flex items-center justify-center","text-md font-extrabold p-0.5 place-items-center relative text-center","w-16 flex-shrink-0","transition-colors duration-200 ease-in-out","hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,children:e}),L=[{id:"Google",icon:u.DSS,label:"Google Search"}],P=({children:e,onClick:t,isActive:s,title:a})=>(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)("div",{className:(0,g.cn)("border rounded-lg text-[var(--text)]","cursor-pointer flex items-center justify-center","p-2 place-items-center relative","w-8 h-8 flex-shrink-0","transition-colors duration-200 ease-in-out",s?"bg-[var(--active)] text-[var(--text)] border-[var(--active)] hover:brightness-95":"bg-transparent border-[var(--text)]/50 hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,"aria-label":a,children:e})}),(0,n.jsx)(h.ZI,{side:"top",className:"bg-[var(--active)]/80 text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:a})})]}),O=()=>{const[e,t]=(0,r.useState)([]),[s,a]=(0,r.useState)(""),[i,u]=(0,r.useState)(z()),[E,_]=(0,r.useState)(""),[O,D]=(0,r.useState)(""),[I,U]=(0,r.useState)(!1),[F,q]=(0,r.useState)(!1),[W,B]=(0,r.useState)(!1),{config:V,updateConfig:H}=(0,w.UK)(),[G,Y]=(0,r.useState)(!1),[J,K]=(0,r.useState)({id:null,url:""}),Z=(0,r.useRef)(null),Q=(0,r.useRef)({id:null,url:""}),[X,ee]=(0,r.useState)(!1),[te,se]=(0,r.useState)(!1),[ae,ne]=(0,r.useState)("idle"),[re,oe]=(0,r.useState)(!1),ie=(0,r.useRef)(null);(0,r.useEffect)((()=>{const e=new ResizeObserver((()=>{Z.current&&(Z.current.style.minHeight="100dvh",requestAnimationFrame((()=>{Z.current&&(Z.current.style.minHeight="")})))}));return Z.current&&e.observe(Z.current),()=>e.disconnect()}),[]),(0,r.useEffect)((()=>{if("page"!==V?.chatMode)return;const e=async()=>{const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(e?.id&&e.url)return e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||e.url.startsWith("about:")?(Q.current.id===e.id&&Q.current.url===e.url||(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")),Q.current={id:e.id,url:e.url},void K({id:e.id,url:e.url})):void(e.id===Q.current.id&&e.url===Q.current.url||(Q.current={id:e.id,url:e.url},K({id:e.id,url:e.url}),await T()))};e();const t=t=>{chrome.tabs.get(t.tabId,(t=>{chrome.runtime.lastError?console.warn(`[Cognito ] Error getting tab info on activation: ${chrome.runtime.lastError.message}`):e()}))},s=(t,s,a)=>{a.active&&("complete"===s.status||s.url&&"complete"===a.status)&&e()};return chrome.tabs.onActivated.addListener(t),chrome.tabs.onUpdated.addListener(s),()=>{chrome.tabs.onActivated.removeListener(t),chrome.tabs.onUpdated.removeListener(s),Q.current={id:null,url:""}}}),[V?.chatMode]),(0,r.useEffect)((()=>{const e=e=>{if(!(F||W||G)&&e.ctrlKey&&"m"===e.key.toLowerCase()){e.preventDefault();const t=V?.chatMode;let s="";"web"===t?(H({chatMode:void 0}),s="Switched to Chat Mode"):"page"===t?(H({chatMode:"web"}),s="Switched to Web Mode"):(H({chatMode:"page"}),s="Switched to Page Mode"),ie.current&&o.oR.dismiss(ie.current),ie.current=(0,o.oR)(s)}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[V?.chatMode,H,F,W]),(0,r.useEffect)((()=>{const e=(e,t,s)=>"ACTIVATE_NOTE_SYSTEM_VIEW"===e.type&&(console.log("[Cognito.tsx] Received ACTIVATE_NOTE_SYSTEM_VIEW. Switching to Note System mode."),q(!1),B(!1),Y(!0),s({status:"ACTIVATING_NOTE_SYSTEM_VIEW_ACK"}),!0);return chrome.runtime.onMessage.addListener(e),()=>{chrome.runtime.onMessage.removeListener(e)}}),[q,B,Y]);const{appendToNote:le}=(0,M.e)();(0,r.useEffect)((()=>{const e=chrome.runtime.connect({name:$.A.SidePanelPort}),t=e=>{"ADD_SELECTION_TO_NOTE"===e.type&&e.payload&&le(e.payload)};return e.onMessage.addListener(t),e.postMessage({type:"init"}),()=>{e.onMessage.removeListener(t),e.disconnect()}}),[le]);const{chatTitle:ce,setChatTitle:de}=(0,p.S)(I,e,s),{onSend:ue,onStop:me}=(0,f.A)(I,s,e,E,V,t,a,_,D,U,ne);(0,x.N)();const he=()=>{t([]),D(""),_(""),U(!1),H({chatMode:void 0,computeLevel:"low"}),ne("idle"),a(""),de(""),u(z()),B(!1),q(!1),Y(!1),Z.current&&(Z.current.scrollTop=0)},ge=async()=>{try{const t=(await l().keys()).filter((e=>e.startsWith("chat_")));if(0===t.length&&0===e.length)return;await Promise.all(t.map((e=>l().removeItem(e)))),o.oR.success("Deleted all chats"),he()}catch(e){console.error("[Cognito] Error deleting all chats:",e),o.oR.error("Failed to delete chats")}};(0,r.useEffect)((()=>{if(e.length>0&&!W&&!F&&!G){const t={id:i,title:ce||`Chat ${new Date(Date.now()).toLocaleString()}`,turns:e,last_updated:Date.now(),model:V?.selectedModel,chatMode:V?.chatMode,webMode:"web"===V?.chatMode?V.webMode:void 0,useNoteActive:V?.useNote,noteContentUsed:V?.useNote?V.noteContent:void 0};l().setItem(i,t).catch((e=>{console.error(`[Cognito ] Error saving chat ${i}:`,e)}))}}),[i,e,ce,V?.selectedModel,V?.chatMode,V?.webMode,V?.useNote,V?.noteContent,W,F]),(0,r.useEffect)((()=>{if("done"===ae||"idle"===ae){const e=setTimeout((()=>{ne("idle")}),1500);return()=>clearTimeout(e)}}),[ae]),(0,r.useEffect)((()=>{let e=!1;return(async()=>{if(!e){he();try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});!e&&t?.id&&t.url?(K({id:t.id,url:t.url}),(t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://")||t.url.startsWith("about:"))&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})):e||(Q.current={id:null,url:""},K({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}catch(t){e||(console.error("[Cognito - Revised] Error during panel open tab check:",t),Q.current={id:null,url:""},K({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}}})(),()=>{e=!0,k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),he(),Q.current={id:null,url:""}}}),[]);const pe=(0,r.useCallback)((()=>{oe(!1)}),[]);return(0,n.jsx)(h.Bc,{delayDuration:300,children:(0,n.jsxs)("div",{ref:Z,className:(0,g.cn)("w-full h-dvh p-0 overflow-hidden","flex flex-col bg-[var(--bg)]"),children:[(0,n.jsx)(y.Y,{chatTitle:ce,deleteAll:ge,downloadImage:()=>(0,S.GV)(e),downloadJson:()=>(0,S.xD)(e),downloadText:()=>(0,S.mR)(e),downloadMarkdown:()=>(0,S.ii)(e),historyMode:W,reset:he,setHistoryMode:B,setSettingsMode:q,settingsMode:F,noteSystemMode:G,onAddNewNoteRequest:G?()=>oe(!0):void 0,setNoteSystemMode:Y,chatMode:V?.chatMode||"chat",chatStatus:ae}),(0,n.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative",children:[F&&(0,n.jsx)(C.w,{}),!F&&W&&!G&&(0,n.jsx)(b.D,{className:"flex-1 w-full min-h-0",loadChat:e=>{de(e.title||""),t(e.turns),u(e.id),B(!1),ne("idle"),q(!1);const s={useNote:e.useNoteActive??!1,noteContent:e.noteContentUsed||""};H(s),"page"!==s.chatMode&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})},onDeleteAll:ge}),!F&&!W&&G&&(0,n.jsx)(A.z,{triggerOpenCreateModal:re,onModalOpened:pe}),!F&&!W&&!G&&(0,n.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 relative",children:[(0,n.jsx)(N.B,{isLoading:I,turns:e,settingsMode:F,onReload:()=>{t((e=>{if(e.length<2)return e;const t=e[e.length-1],s=e[e.length-2];return"assistant"===t.role&&"user"===s.role?(a(s.rawContent),e.slice(0,-2)):e})),U(!1),ne("idle")},onEditTurn:(e,s)=>{t((t=>{const a=[...t];return a[e]&&(a[e]={...a[e],rawContent:s}),a}))}}),0===e.length&&!V?.chatMode&&(0,n.jsxs)("div",{className:"fixed bottom-20 left-8 flex flex-col gap-2 z-[5]",children:[(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{asChild:!0,children:(0,n.jsx)(m.$,{"aria-label":"Cycle compute level",variant:"ghost",size:"icon",onClick:()=>{const e=V.computeLevel;H({computeLevel:"low"===e?"medium":"medium"===e?"high":"low"})},className:(0,g.cn)("hover:bg-secondary/70","high"===V.computeLevel?"text-red-600":"medium"===V.computeLevel?"text-orange-300":"text-[var(--text)]"),children:(0,n.jsx)(d.cfR,{})})}),(0,n.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)] max-w-80",children:(0,n.jsx)("p",{children:`Compute Level: ${V.computeLevel?.toUpperCase()}. Click to change. [Warning]: beta feature and resource costly.`})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{asChild:!0,children:(0,n.jsx)(m.$,{"aria-label":"Add Web Search Results to LLM Context",variant:"ghost",size:"icon",onClick:()=>{H({chatMode:"web",webMode:V.webMode||L[0].id})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,n.jsx)(c.pqQ,{})})}),(0,n.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,n.jsx)("p",{children:"Add Web Search Results to LLM Context"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{asChild:!0,children:(0,n.jsx)(m.$,{"aria-label":"Add Current Web Page to LLM Context",variant:"ghost",size:"icon",onClick:()=>{H({chatMode:"page"})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,n.jsx)(c.RGv,{})})}),(0,n.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,n.jsx)("p",{children:"Add Current Web Page to LLM Context"})})]})]}),"page"===V?.chatMode&&(0,n.jsx)("div",{className:(0,g.cn)("fixed bottom-16 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-8 z-[2]","transition-all duration-200 ease-in-out",X?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>ee(!0),onMouseLeave:()=>ee(!1),children:(0,n.jsxs)("div",{className:"flex items-center space-x-6 max-w-full overflow-x-auto px-0",children:[(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(R,{onClick:()=>ue("Provide your summary."),children:"TLDR"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Quick Summary"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(R,{onClick:()=>ue("Extract all key figures, names, locations, and dates mentioned on this page and list them."),children:"Facts"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Numbers, events, names"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(R,{onClick:()=>ue("Find positive developments, achievements, or opportunities mentioned on this page."),children:"Yay!"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Good news"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(R,{onClick:()=>ue("Find concerning issues, risks, or criticisms mentioned on this page."),children:"Oops"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Bad news"})})]})]})}),"web"===V?.chatMode&&(0,n.jsx)("div",{className:(0,g.cn)("fixed bottom-14 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-10 z-[2]","transition-all duration-200 ease-in-out",te?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>se(!0),onMouseLeave:()=>se(!1),children:(0,n.jsx)("div",{className:"flex items-center space-x-4 max-w-full overflow-x-auto px-4 py-1",children:L.map((e=>(0,n.jsx)(P,{onClick:()=>{H({webMode:e.id,chatMode:"web"})},isActive:V.webMode===e.id,title:e.label,children:(0,n.jsx)(e.icon,{size:18})},e.id)))})})]})]}),!F&&!W&&!G&&(0,n.jsx)("div",{className:"p-2 relative z-[10]",children:(0,n.jsx)(j.p,{isLoading:I,message:s,setMessage:a,onSend:()=>ue(s),onStopRequest:me})}),V?.backgroundImage?(0,n.jsx)(v.V,{}):null,(0,n.jsx)(o.l$,{containerStyle:{borderRadius:16,bottom:"60px"},toastOptions:{duration:2e3,position:"bottom-center",style:{background:"var(--bg)",color:"var(--text)",fontSize:"1rem",border:"1px solid var(--text)",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},success:{duration:2e3,style:{background:"var(--bg)",color:"var(--text)",fontSize:"1.25rem"}}}})]})})};a()}catch(D){a(D)}}))},2090:(e,t,s)=>{s.d(t,{$:()=>l});var a=s(4848),n=(s(6540),s(3362)),r=s(2732),o=s(5284);const i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none not-focus-visible",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 not-focus-visible",outline:"border bg-background shadow-xs hover:bg-accent",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:text-foreground hover:bg-black/10 dark:hover:bg-white/10",link:"text-primary underline-offset-4 hover:underline","message-action":"bg-transparent text-muted-foreground p-0 shadow-none hover:bg-transparent focus:bg-transparent active:text-muted active:[&_svg]:text-muted-foreground transition-colors duration-75","ghost-themed":"text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground","outline-themed":"border border-[var(--active)] bg-transparent text-[var(--active)] shadow-xs hover:bg-[var(--active)]/20 focus-visible:bg-[var(--active)]/20 focus-visible:ring-1 focus-visible:ring-[var(--active)]","destructive-outline":"border border-destructive bg-transparent text-destructive shadow-xs hover:bg-destructive/10 hover:text-destructive-foreground focus-visible:bg-destructive/10 focus-visible:text-destructive-foreground focus-visible:ring-1 focus-visible:ring-destructive","copy-button":"bg-background text-foreground shadow-none hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground",connect:"bg-[var(--input-background)] text-[var(--text)] hover:bg-[var(--active)]/90 shadow-sm focus-visible:ring-1 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]","active-bordered":"bg-[var(--active)] text-[var(--text)] border border-[var(--text)] hover:brightness-110 focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm","outline-subtle":"border border-[var(--text)]/50 bg-transparent text-[var(--text)] hover:bg-[var(--text)]/10 hover:border-[var(--text)]/70 hover:text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm"},size:{default:"h-9 px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",sm:"h-8 rounded-md px-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-4",lg:"h-10 rounded-md px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",icon:"size-8 [&_svg:not([class*='size-'])]:size-7",xs:"h-6 w-6 p-0 rounded-sm [&_svg:not([class*='size-'])]:size-3.5 text-xs"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:r=!1,...l}){const c=r?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:s,className:e})),...l})}},2353:(e,t,s)=>{s.d(t,{w:()=>Z});var a=s(4848),n=s(6540),r=s(1584),o=s(5107),i=s(5284);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"accordion",...e})}function c({className:e,...t}){return(0,a.jsx)(r.q7,{"data-slot":"accordion-item",className:(0,i.cn)("border-b last:border-b-0","bg-[var(--input-background)]","shadow-md","rounded-xl","border-[var(--text)]/10",e),...t})}function d({className:e,children:t,...s}){return(0,a.jsx)(r.Y9,{className:"flex",children:(0,a.jsxs)(r.l9,{"data-slot":"accordion-trigger",className:(0,i.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",e),...s,children:[t,(0,a.jsx)(o.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function u({className:e,children:t,...s}){return(0,a.jsx)(r.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...s,children:(0,a.jsx)("div",{className:(0,i.cn)("pt-0 pb-4",e),children:t})})}var m=s(6948),h=s(888),g=s(3),p=s(2090),f=s(6532);const x=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[s,r]=(0,n.useState)(e?.ollamaUrl||"http://localhost:11434"),[o,l]=(0,n.useState)(!1),c=()=>{l(!0),h.Ay.dismiss(),h.Ay.loading("Connecting to Ollama..."),fetch(`${s}/api/tags`).then((e=>e.ok?e.json():e.json().then((t=>{throw new Error(t?.error||`Connection failed: ${e.status} ${e.statusText}`)})).catch((()=>{throw new Error(`Connection failed: ${e.status} ${e.statusText}`)})))).then((a=>{Array.isArray(a.models)?(t({ollamaConnected:!0,ollamaUrl:s,ollamaError:void 0,models:(e?.models||[]).filter((e=>"ollama_generic"!==e.id)).concat([{id:"ollama_generic",host:"ollama",active:!0,name:"Ollama Model"}]),selectedModel:"ollama_generic"}),h.Ay.dismiss(),h.Ay.success("Connected to ollama")):a?.error?(t({ollamaError:a.error,ollamaConnected:!1}),h.Ay.dismiss(),h.Ay.error("string"==typeof a.error?a.error:"Ollama connection error")):(t({ollamaError:"Unexpected response from Ollama",ollamaConnected:!1}),h.Ay.dismiss(),h.Ay.error("Unexpected response from Ollama"))})).catch((e=>{h.Ay.dismiss(),h.Ay.error(e.message||"Failed to connect to Ollama"),t({ollamaError:e.message,ollamaConnected:!1})})).finally((()=>{l(!1)}))},d=e?.ollamaConnected;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(f.p,{id:"ollama-url-input",value:s,onChange:e=>r(e.target.value),placeholder:"http://localhost:11434",className:"pr-8",disabled:o}),!d&&(0,a.jsx)(p.$,{onClick:c,variant:"connect",size:"sm",disabled:o,children:o?"...":"Connect"}),d&&(0,a.jsx)(p.$,{variant:"ghost",size:"sm","aria-label":"Connected to Ollama",className:(0,i.cn)("w-8 rounded-md text-[var(--success)]"),disabled:o,onClick:c,children:(0,a.jsx)(g.YrT,{className:"h-5 w-5"})})]})},v=({text:e="",widget:t=(0,a.jsx)(a.Fragment,{}),icon:s=""})=>(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[s&&(0,a.jsx)("span",{className:(0,i.cn)("text-foreground","text-xl","leading-none","mr-3"),children:s}),(0,a.jsx)("span",{className:(0,i.cn)("text-foreground","opacity-90","font-['Space_Mono',_monospace]","text-base","font-bold"),children:e})]}),t&&(0,a.jsx)("div",{className:"ml-2",children:t})]}),b=({title:e,Component:t})=>(0,a.jsxs)("div",{className:"px-4 py-3 border-b border-[var(--text)]/10 last:border-b-0",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-2",children:(0,a.jsx)("h4",{className:"text-base font-medium capitalize text-foreground",children:e})}),(0,a.jsx)(t,{})]}),w=()=>(0,a.jsxs)(c,{value:"connect",className:(0,i.cn)("bg-[var(--input-background)]","border-[var(--text)]/10","rounded-xl","shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105","overflow-hidden"),children:[(0,a.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,a.jsx)(v,{icon:"♾️",text:"API Access"})}),(0,a.jsx)(u,{className:"p-0 text-[var(--text)]",children:(0,a.jsx)(b,{Component:x,title:"Ollama"})})]});var y=s(803),j=s(2732);const N=(0,j.F)("relative flex w-full touch-none select-none items-center data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",{variants:{variant:{default:["[&>span[data-slot=slider-track]]:bg-secondary","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-primary","[&>button[data-slot=slider-thumb]]:bg-background","[&>button[data-slot=slider-thumb]]:border-primary","[&>button[data-slot=slider-thumb]]:ring-offset-background","[&>button[data-slot=slider-thumb]]:focus-visible:ring-ring"],themed:["[&>span[data-slot=slider-track]]:bg-[var(--text)]/10","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:border-[var(--text)]/50","[&>button[data-slot=slider-thumb]]:ring-offset-[var(--bg)]","[&>button[data-slot=slider-thumb]]:focus-visible:ring-[var(--active)]"]}},defaultVariants:{variant:"default"}});function S({className:e,variant:t,defaultValue:s,value:r,min:o=0,max:l=100,...c}){const d=n.useMemo((()=>Array.isArray(r)?r:Array.isArray(s)?s:[o,l]),[r,s,o]);return(0,a.jsxs)(y.bL,{"data-slot":"slider",defaultValue:s,value:r,min:o,max:l,className:(0,i.cn)(N({variant:t,className:e})),...c,children:[(0,a.jsx)(y.CC,{"data-slot":"slider-track",className:(0,i.cn)("relative h-1.5 w-full grow overflow-hidden rounded-full","data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,a.jsx)(y.Q6,{"data-slot":"slider-range",className:(0,i.cn)("absolute h-full","data-[orientation=vertical]:w-full")})}),(d.length>0?d:[o]).map(((e,t)=>(0,a.jsx)(y.zi,{"data-slot":"slider-thumb",className:(0,i.cn)("block h-4 w-4 bg-white rounded-full border border-primary/50 shadow-sm transition-colors focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50")},t)))]})}const C=({size:e,updateConfig:t})=>(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-6 text-left",children:["Char Limit:"," ",(0,a.jsx)("span",{className:"font-normal",children:128===e?"inf":`${e}k`})]}),(0,a.jsx)(S,{defaultValue:[e],max:128,min:1,step:1,variant:"themed",onValueChange:e=>t({contextLimit:e[0]})})]}),k=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),s=e?.contextLimit||1;return(0,a.jsxs)(c,{value:"page-context",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","overflow-hidden","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,a.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,a.jsx)(v,{icon:"📃",text:"Page Context"})}),(0,a.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,a.jsx)(C,{size:s,updateConfig:t})})]})};var $=s(5634);const M=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),s=e=>s=>{const a=Array.isArray(s)?s[0]:s;t({[e]:a})},n=e.temperature??.7,r=e.maxTokens??32048,o=e.topP??.95,l=e.presencepenalty??0;return(0,a.jsxs)(c,{value:"model-params",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,a.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,a.jsx)(v,{icon:"⚙️",text:"Model Config"})}),(0,a.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)($.J,{htmlFor:"temperature",className:"text-base font-medium text-foreground",children:["Temperature (",n.toFixed(2),")"]}),(0,a.jsx)(S,{id:"temperature",min:0,max:2,step:.01,value:[n],onValueChange:s("temperature"),variant:"themed"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)($.J,{htmlFor:"maxTokens",className:"text-base font-medium text-foreground",children:["Max Tokens (",r,")"]}),(0,a.jsx)(f.p,{id:"maxTokens",type:"number",value:r,min:1,max:128e4,onChange:e=>s("maxTokens")(parseInt(e.target.value,10)||0),className:(0,i.cn)("hide-number-spinners")})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)($.J,{htmlFor:"topP",className:"text-base font-medium text-foreground",children:["Top P (",o.toFixed(2),")"]}),(0,a.jsx)(S,{id:"topP",min:0,max:1,step:.01,value:[o],onValueChange:s("topP"),variant:"themed"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)($.J,{htmlFor:"presencepenalty",className:"text-base font-medium text-foreground",children:["Presence Penalty (",l.toFixed(2),")"]}),(0,a.jsx)(S,{id:"presencepenalty",min:-2,max:2,step:.01,value:[l],onValueChange:s("presencepenalty"),variant:"themed"})]})]})})]})};var A=s(7086),E=s(9018),_=s(3732),T=s(9696);const z=({hasChange:e,onSave:t,onSaveAs:s,onCancel:n})=>e?(0,a.jsxs)("div",{className:"flex mt-4 space-x-2 justify-end w-full",children:[(0,a.jsx)(p.$,{variant:"active-bordered",size:"sm",onClick:t,children:"Save"}),(0,a.jsx)(p.$,{variant:"active-bordered",size:"sm",onClick:s,children:"Save As..."}),(0,a.jsx)(p.$,{variant:"outline-subtle",size:"sm",onClick:n,children:"Cancel"})]}):null,R=({isOpen:e,onOpenChange:t,personaPrompt:s,personas:r,updateConfig:o,onModalClose:l})=>{const[c,d]=(0,n.useState)("");return(0,a.jsx)(A.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(A.Cf,{className:(0,i.cn)("sm:max-w-[425px]","bg-[var(--bg)]"),onCloseAutoFocus:e=>e.preventDefault(),children:[(0,a.jsx)(A.c7,{children:(0,a.jsx)(A.L3,{className:"text-[var(--text)]",children:"Create New Persona"})}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsx)($.J,{htmlFor:"persona-name",className:"text-base font-medium text-foreground sr-only",children:"Persona Name"}),(0,a.jsx)(f.p,{id:"persona-name",placeholder:"Enter persona name",value:c,onChange:e=>d(e.target.value),className:(0,i.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,a.jsxs)(A.Es,{className:"sm:justify-end",children:[(0,a.jsx)(p.$,{type:"button",variant:"outline-subtle",size:"sm",onClick:l,children:" Cancel "}),(0,a.jsx)(p.$,{type:"button",variant:"active-bordered",size:"sm",className:(0,i.cn)(),disabled:!c.trim(),onClick:()=>{c.trim()&&(o({personas:{...r,[c.trim()]:s},persona:c.trim()}),d(""),l())},children:" Create "})]})]})})},L=({isOpen:e,onOpenChange:t,persona:s,personas:n,updateConfig:r,onModalClose:o})=>(0,a.jsx)(A.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(A.Cf,{className:(0,i.cn)("sm:max-w-[425px]","bg-[var(--bg)]","border","text-[var(--text)]"),children:[(0,a.jsxs)(A.c7,{children:[(0,a.jsxs)(A.L3,{className:"text-[var(--text)]",children:['Delete "',s,'"']}),(0,a.jsx)(A.rr,{className:"text-[var(--text)]/80 pt-2",children:"Are you sure you want to delete this persona? This action cannot be undone."})]}),(0,a.jsxs)(A.Es,{className:"sm:justify-end pt-4",children:[(0,a.jsx)(p.$,{type:"button",variant:"outline-subtle",size:"sm",onClick:o,children:" Cancel "}),(0,a.jsx)(p.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{const e={...n};delete e[s];const t=Object.keys(e);r({personas:e,persona:t.length>0?t[0]:"Ein"}),o()},children:" Delete "})]})]})}),P=({personas:e,persona:t,updateConfig:s})=>(0,a.jsxs)(E.l6,{value:t,onValueChange:e=>s({persona:e}),children:[(0,a.jsx)(E.bq,{variant:"settings",className:(0,i.cn)("flex w-full","data-[placeholder]:text-muted-foreground"),children:(0,a.jsx)(E.yv,{placeholder:"Select persona"})}),(0,a.jsx)(E.gC,{variant:"settingsPanel",className:(0,i.cn)(),children:Object.keys(e).map((e=>(0,a.jsxs)(E.eb,{value:e,focusVariant:"activeTheme",children:[" ",e," "]},e)))})]}),O=({personaPrompt:e,setPersonaPrompt:t,isEditing:s,setIsEditing:n})=>{const r={onFocus:e=>{s||n(!0)}};return(0,a.jsx)(T.T,{autosize:!0,minRows:3,maxRows:8,value:e,onChange:e=>{s||n(!0),t(e.target.value)},readOnly:!s,...r,"data-slot":"textarea-default",placeholder:"Define the persona's characteristics and instructions here...",className:(0,i.cn)("w-full min-h-[80px] border border-[var(--text)]/10 px-3 py-2 text-sm ring-offset-[var(--bg)] placeholder:text-[var(--muted-foreground)] rounded-[12px]","text-[var(--text)]","no-scrollbar","focus-visible:outline-none focus-visible:ring-0 focus-visible:box-shadow-[inset_0_0_0_1px_rgba(255,255,255,0.1),_0_0_8px_rgba(168,123,255,0.3)]",s?"hover:border-[var(--active)] focus:border-[var(--active)]":"opacity-75 cursor-default")})},D=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[s,r]=(0,n.useState)(!1),[o,l]=(0,n.useState)(!1),[h,g]=(0,n.useState)(!1),f=e?.personas||{Ein:"You are Ein, a helpful AI assistant."},x=e?.persona||"Ein",b=f?.[x]??f?.Ein??"You are Ein, a helpful AI assistant.",[w,y]=(0,n.useState)(b),j=h&&w!==b;return(0,n.useEffect)((()=>{y(f?.[x]??f?.Ein??""),g(!1)}),[x,JSON.stringify(f)]),(0,a.jsxs)(c,{value:"persona",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,a.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,a.jsx)(v,{icon:"🥷",text:"Persona"})}),(0,a.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(P,{persona:x,personas:f,updateConfig:t}),(0,a.jsxs)(p.$,{variant:"ghost",size:"sm","aria-label":"Add new persona",className:(0,i.cn)("text-[var(--text)] p-1.5 rounded-md","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>{y(""),g(!0),r(!0)},children:[" ",(0,a.jsx)(_.YHj,{className:"h-5 w-5"})," "]}),Object.keys(f).length>1&&(0,a.jsxs)(p.$,{variant:"ghost",size:"sm","aria-label":"Delete current persona",className:(0,i.cn)("text-[var(--text)] hover:text-[var(--error)] hover:bg-[var(--error)]/10 p-1.5 rounded-md","focus-visible:ring-1 focus-visible:ring-[var(--error)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>l(!0),children:[" ",(0,a.jsx)(_.dW_,{className:"h-5 w-5"})," "]})]}),(0,a.jsx)(O,{personaPrompt:w,setPersonaPrompt:y,isEditing:h,setIsEditing:g}),(0,a.jsx)(z,{hasChange:j,onSave:()=>{t({personas:{...f,[x]:w}}),g(!1)},onSaveAs:()=>{r(!0)},onCancel:()=>{y(b),g(!1)}})]})}),(0,a.jsx)(R,{isOpen:s,onOpenChange:e=>{r(e),e||(y(b),g(!1))},personaPrompt:w,personas:f,updateConfig:t,onModalClose:()=>r(!1)}),(0,a.jsx)(L,{isOpen:o,onOpenChange:l,persona:x,personas:f,updateConfig:t,onModalClose:()=>l(!1)})]})};var I=s(1104),U=s(5002);const F=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[s,r]=(0,n.useState)([]),[o,l]=(0,n.useState)(!0),[h,g]=(0,n.useState)(null);(0,n.useEffect)((()=>{l(!0),g(null),(0,U.F2)().then((s=>{if(r(s),!e.tts?.selectedVoice&&s.length>0){const a=s.find((e=>e.lang.startsWith("en")))||s[0];a&&t({tts:{...e.tts,selectedVoice:a.name}})}})).catch((e=>{console.error("Error loading TTS voices:",e),g("Could not load voices. TTS might not be available.")})).finally((()=>{l(!1)}))}),[]);const p=e.tts?.rate??1;return(0,a.jsxs)(c,{value:"tts-settings",className:(0,i.cn)("bg-[var(--input-background)]","border-[var(--text)]/10","rounded-xl","shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,a.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,a.jsx)(v,{text:"Text-to-Speech",icon:"🎙️"})}),(0,a.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[o?(0,a.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,a.jsx)(I.A,{className:"h-6 w-6 animate-spin text-[var(--text)]"})}):h?(0,a.jsx)("p",{className:"text-[var(--error)] text-base font-medium",children:h}):s.length>0?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)($.J,{className:"text-base font-medium text-foreground",children:"Voice"}),(0,a.jsxs)(E.l6,{value:e.tts?.selectedVoice||"",onValueChange:s=>{t({tts:{...e.tts,selectedVoice:s}})},children:[(0,a.jsx)(E.bq,{variant:"settings",className:(0,i.cn)("w-full","data-[placeholder]:text-muted-foreground"),children:(0,a.jsx)(E.yv,{placeholder:"Select voice"})}),(0,a.jsx)(E.gC,{variant:"settingsPanel",children:s.map((e=>(0,a.jsxs)(E.eb,{value:e.name,focusVariant:"activeTheme",children:[e.name," (",e.lang,")"]},e.name)))})]})]}):(0,a.jsx)("p",{className:"text-base font-medium text-foreground",children:"No voices available in this browser."}),!o&&!h&&s.length>0&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)($.J,{className:"text-base font-medium text-foreground pb-3 block",children:["Speech Rate: ",p.toFixed(1)]}),(0,a.jsx)(S,{min:.5,max:2,step:.1,value:[p],onValueChange:s=>{t({tts:{...e.tts,rate:s[0]}})},variant:"themed"})]})]})})]})};var q=s(9451),W=s(8309);function B({className:e,...t}){return(0,a.jsx)(q.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",e),...t})}const V=(0,j.F)("aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:["border-input dark:bg-input/30 text-primary focus-visible:border-ring focus-visible:ring-ring/50"],themed:["border-[var(--text)] text-[var(--active)]","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-0 focus-visible:border-[var(--active)]","data-[state=checked]:border-[var(--active)]"]}},defaultVariants:{variant:"default"}});function H({className:e,variant:t,...s}){return(0,a.jsx)(q.q7,{"data-slot":"radio-group-item",className:(0,i.cn)(V({variant:t,className:e})),...s,children:(0,a.jsx)(q.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,a.jsx)(W.A,{className:(0,i.cn)("absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2","themed"===t?"fill-[var(--active)]":"fill-primary")})})})}const G=({webMode:e,updateConfig:t})=>(0,a.jsx)(B,{value:e,onValueChange:e=>t({webMode:e}),className:"w-1/2 space-y-3",children:["Google"].map((e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(H,{value:e,id:`webMode-${e}`,variant:"themed"}),(0,a.jsx)($.J,{htmlFor:`webMode-${e}`,className:"text-[var(--text)] text-base font-medium cursor-pointer",children:e})]},e)))}),Y=({config:e,updateConfig:t})=>{const s=e?.webLimit??16,n=e?.serpMaxLinksToVisit??3;return(0,a.jsxs)("div",{className:"w-full space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-2 text-left",children:["Max Links to Visit: ",(0,a.jsx)("span",{className:"font-normal",children:n})]}),(0,a.jsx)(S,{value:[n],max:10,min:1,step:1,variant:"themed",onValueChange:e=>t({serpMaxLinksToVisit:e[0]})}),(0,a.jsx)("p",{className:"text-[var(--text)]/70 text-xs pt-1",children:"Number of search result links to fetch."})]}),(0,a.jsxs)("div",{className:"pt-2",children:[(0,a.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-2 text-left",children:["Content Char Limit:"," ",(0,a.jsx)("span",{className:"font-normal",children:128===s?"Unlimited (Full)":`${s}k`})]}),(0,a.jsx)(S,{value:[s],max:128,min:1,step:1,variant:"themed",onValueChange:e=>t({webLimit:e[0]})}),(0,a.jsx)("p",{className:"text-[var(--text)]/70 text-xs pt-1",children:"Max characters (in thousands) of content to use. 128k for 'Unlimited'."})]})]})},J=()=>{const{config:e,updateConfig:t}=(0,m.UK)();return(0,n.useEffect)((()=>{if("Google"===e?.webMode){const s={};void 0===e?.serpMaxLinksToVisit&&(s.serpMaxLinksToVisit=3),void 0===e?.webLimit&&(s.webLimit=16),Object.keys(s).length>0&&t(s)}}),[e?.webMode,e?.serpMaxLinksToVisit,e?.webLimit,t]),(0,a.jsxs)(c,{value:"web-search",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","overflow-hidden","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,a.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,a.jsx)(v,{icon:"🌐",text:"Web Search"})}),(0,a.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(G,{updateConfig:t,webMode:e?.webMode}),"Google"===e?.webMode?(0,a.jsx)("div",{className:"w-[45%] pl-4 flex flex-col space-y-6",children:(0,a.jsx)(Y,{config:e,updateConfig:t})}):(0,a.jsx)("div",{className:"w-[45%] pl-4",children:(0,a.jsx)("p",{className:"text-[var(--text)]/70",children:"Select a search mode to see its options."})})]})})]})};var K=s(116);const Z=()=>{const{config:e}=(0,m.UK)(),[t,s]=(0,n.useState)(!e?.models||0===e.models.length),[r,o]=(0,n.useState)("");return(0,a.jsxs)("div",{id:"settings",className:"relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-transparent text-foreground px-6 pb-10 pt-[56px] scrollbar-hidden",children:[(0,a.jsx)(K.A,{}),t&&(0,a.jsx)("div",{className:(0,i.cn)("mb-4 p-4","rounded-[3rem]","cognito-title-blade-glow","text-[var(--text)]","text-base"),children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:"Quick Setup Guide"}),(0,a.jsxs)("div",{className:"flex flex-col gap-3 w-full",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-[var(--active)] flex items-center justify-center",children:"1"}),(0,a.jsx)("p",{children:" Fill your API key or urls in API Access"})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-[var(--active)] flex items-center justify-center",children:"2"}),(0,a.jsx)("p",{children:"Exit settings, then click the avatar icon to select your model to chat with. You can set username in the top right corner."})]}),(0,a.jsx)("div",{className:"text-sm text-[var(--mute)] mt-1 ml-9",children:(0,a.jsx)("em",{children:"Note: You can change the other settings now or later. Have fun!"})})]}),(0,a.jsx)(p.$,{variant:"outline",className:"justify-center px-8 py-2 text-sm rounded-full mt-2",onClick:()=>{o("connect"),s(!1)},children:"Got It"})]})}),(0,a.jsxs)(l,{type:"single",collapsible:!0,className:"w-full flex flex-col gap-4",value:r,onValueChange:o,children:[(0,a.jsx)(w,{}),(0,a.jsx)(M,{}),(0,a.jsx)(D,{}),(0,a.jsx)(F,{}),(0,a.jsx)(k,{}),(0,a.jsx)(J,{}),(0,a.jsx)("div",{className:"pointer-events-none h-12"})," "]})]})}},2955:(e,t,s)=>{s.d(t,{VZ:()=>l,oK:()=>i,s2:()=>o});var a=s(3790),n=s.n(a);const r="cognito_note_",o=async e=>{const t=Date.now(),s=e.id||`${r}${Date.now()}_${Math.random().toString(16).slice(2)}`,a=e.id?await n().getItem(s):null,o={id:s,title:e.title||`Note - ${new Date(t).toLocaleDateString([],{year:"numeric",month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit"})}`,content:e.content,createdAt:a?.createdAt||t,lastUpdatedAt:t,tags:e.tags};return await n().setItem(s,o),o},i=async()=>{const e=(await n().keys()).filter((e=>e.startsWith(r))),t=[];for(const s of e){const e=await n().getItem(s);e&&t.push(e)}return t.sort(((e,t)=>t.lastUpdatedAt-e.lastUpdatedAt))},l=async e=>{await n().removeItem(e),console.log("Note deleted from system:",e)}},3003:(e,t,s)=>{s.a(e,(async(e,t)=>{try{var a=s(4848),n=s(5338),r=s(1468),o=s(3190),i=s(6174),l=s(2050),c=s(6948),d=e([l]);l=(d.then?(await d)():d)[0];const u=(0,o.g)(i.A.ContentPort),m=document.getElementById("root");u.ready().then((()=>{if(null==m)throw new Error("Root container not found");(0,n.createRoot)(m).render((0,a.jsx)(r.Kq,{store:u,children:(0,a.jsx)(c.sG,{children:(0,a.jsx)(l.A,{})})}))})),t()}catch(e){t(e)}}))},3190:(e,t,s)=>{s.d(t,{g:()=>d});var a=s(38),n=s(9448),r=s(7346),o=s(3207),i=s(5886);const l={...s(6108).z2,...i.z2},c=((0,o.nK)(l),r.P,(0,a.N0)(),n.logger,[(0,o.nK)(l),r.P,(0,a.N0)(),n.logger]);(0,o.nK)(l),n.logger;const d=e=>{const t=new o.il({channelName:e});return(0,o.Tw)(t,...c),t}},3193:(e,t,s)=>{s.d(t,{Y:()=>F});var a=s(4848),n=s(6540),r=s(3),o=s(5066),i=s(6948),l=s(5284),c=s(888),d=s(2090),u=s(990),m=s(8697);function h({...e}){return(0,a.jsx)(u.bL,{"data-slot":"sheet",...e})}function g({...e}){return(0,a.jsx)(u.ZL,{"data-slot":"sheet-portal",...e})}function p({className:e,...t}){return(0,a.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}const f=n.forwardRef((({className:e,children:t,side:s="right",variant:n="default",...r},o)=>{const i="themedPanel"===n?((e="right")=>(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full border-l","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full border-r","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t","bg-[var(--bg)] text-[var(--text)] shadow-xl"))(s):((e="right")=>(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t"))(s);return(0,a.jsxs)(g,{children:[(0,a.jsx)(p,{})," ",(0,a.jsxs)(u.UC,{ref:o,"data-slot":"sheet-content",className:(0,l.cn)(i,e),...r,children:[t,(0,a.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(m.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}));function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",e),...t})}function v({className:e,...t}){return(0,a.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",e),...t})}function b({className:e,...t}){return(0,a.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}f.displayName=u.UC.displayName;var w=s(9018),y=s(6532),j=s(3885),N=s(461);function S({className:e,...t}){return(0,a.jsx)(N.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function C({className:e,...t}){return(0,a.jsx)(N._V,{"data-slot":"avatar-image",className:(0,l.cn)("aspect-square size-full",e),...t})}function k({className:e,...t}){return(0,a.jsx)(N.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var $=s(8698),M=s(7520),A=s(116);const E=({isOpen:e,onOpenChange:t,config:s,updateConfig:o,setSettingsMode:i,setHistoryMode:c,setNoteSystemMode:u})=>{const[m,g]=n.useState(""),[N,E]=n.useState(!1),{fetchAllModels:_}=(0,$.N)(),T=n.useRef(null),z=(0,n.useRef)(null),[R,L]=n.useState({top:0,left:0,width:0}),P=s?.persona||"default",O=M.z[P]||M.z.default,D=s?.models?.filter((e=>e.id.toLowerCase().includes(m.toLowerCase())||e.host?.toLowerCase()?.includes(m.toLowerCase())))||[];return(0,n.useEffect)((()=>{e&&(g(""),E(!1))}),[e]),(0,n.useEffect)((()=>{if(N&&z.current){const e=z.current.getBoundingClientRect();L({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}}),[N]),(0,n.useEffect)((()=>{if(!N)return;const e=()=>{if(z.current){const e=z.current.getBoundingClientRect();L({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}};return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}),[N]),(0,a.jsxs)(h,{open:e,onOpenChange:t,children:[(0,a.jsx)(p,{}),(0,a.jsxs)(f,{variant:"themedPanel",side:"left",className:(0,l.cn)("p-0 border-r-0","w-[22.857rem] sm:w-[27.143rem]","flex flex-col h-full max-h-screen","[&>button]:hidden","settings-drawer-content","overflow-y-auto"),style:{height:"100dvh"},ref:T,onOpenAutoFocus:e=>{e.preventDefault(),T.current?.focus({preventScroll:!0})},children:[(0,a.jsx)(A.A,{}),(0,a.jsx)("div",{className:(0,l.cn)("border border-[var(--active)]","sticky top-0 z-10 p-0")}),(0,a.jsxs)(x,{className:"px-4 pt-4 pb-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-2 relative z-10",children:(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{variant:"ghost",size:"sm","aria-label":"Close Settings",className:"text-[var(--text)] rounded-md relative top-[1px]",onClick:()=>t(!1),children:(0,a.jsx)(r.yGN,{size:"20px"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:" Close Settings "})]})}),(0,a.jsx)(v,{className:"text-center font-['Bruno_Ace_SC'] tracking-tight -mt-10 cognito-title-container",children:(0,a.jsxs)("a",{href:"https://github.com/3-ark/Cognito",target:"_blank",rel:"noopener noreferrer",className:(0,l.cn)("text-xl font-semibold text-[var(--text)] bg-[var(--active)] inline-block px-3 py-1 rounded-md no-underline","cognito-title-blade-glow"),children:["COGNITO ",(0,a.jsxs)("sub",{className:"contrast-200 text-[0.5em]",children:["v","3.7.4"]})]})}),(0,a.jsx)(b,{className:"text-center font-['Bruno_Ace_SC'] text-[var(--text)] leading-tight mt-2",children:"Settings"})]}),(0,a.jsxs)("div",{className:(0,l.cn)("flex flex-col h-full overflow-y-auto settings-drawer-body","no-scrollbar"),children:[(0,a.jsxs)("div",{className:(0,l.cn)("flex flex-col space-y-5 flex-1","px-6","py-4"),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex items-center justify-between mt-5 mb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{htmlFor:"persona-select",className:"text-[var(--text)] opacity-80 font-['Bruno_Ace_SC'] text-lg shrink-0",children:"Persona"}),(0,a.jsxs)(S,{className:"h-8 w-8 border border-[var(--active)]",children:[(0,a.jsx)(C,{src:O,alt:P}),(0,a.jsx)(k,{children:P.substring(0,1).toUpperCase()})]})]})}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)(w.l6,{value:P,onValueChange:e=>o({persona:e}),children:[(0,a.jsx)(w.bq,{id:"persona-select",variant:"settingsPanel",className:"w-full font-['Space_Mono',_monospace] data-[placeholder]:text-muted-foreground",children:(0,a.jsx)(w.yv,{placeholder:"Select Persona..."})}),(0,a.jsx)(w.gC,{variant:"settingsPanel",children:Object.keys(s?.personas||{}).map((e=>(0,a.jsx)(w.eb,{value:e,className:(0,l.cn)("hover:brightness-95 focus:bg-[var(--active)]","font-['Space_Mono',_monospace]"),children:e},e)))})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"model-input",className:"block text-[var(--text)] opacity-80 text-lg font-['Bruno_Ace_SC']",children:"Model"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.p,{id:"model-input",ref:z,value:N?m:s?.selectedModel||"",placeholder:N?"Search models...":s?.selectedModel||"Select model...",onChange:e=>g(e.target.value),onFocus:()=>{g(""),E(!0),_()},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9 font-['Space_Mono',_monospace]","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-95","mb-2 mt-3","ring-1 ring-inset ring-[var(--active)]/50")}),N&&(0,a.jsx)("div",{className:"fixed inset-0 z-50",onClick:()=>E(!1),children:(0,a.jsx)("div",{className:(0,l.cn)("absolute left-0 right-0","bg-[var(--bg)]","border border-[var(--active)]/20","rounded-xl shadow-lg","no-scrollbar","overflow-y-auto"),style:{maxHeight:"min(calc(50vh - 6rem), 300px)",top:`${R.top}px`,left:`${R.left}px`,width:`${R.width}px`},onClick:e=>e.stopPropagation(),children:(0,a.jsx)("div",{className:"py-0.5",children:D.length>0?D.map((e=>(0,a.jsx)("button",{type:"button",className:(0,l.cn)("w-full text-left","px-4 py-1.5","text-[var(--text)] text-sm","hover:bg-[var(--active)]/20","focus:bg-[var(--active)]/30","transition-colors duration-150","font-['Space_Mono',_monospace]"),onClick:()=>{o({selectedModel:e.id}),g(""),E(!1)},children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("span",{children:[e.host?`(${e.host}) `:"",e.id,e.context_length&&(0,a.jsxs)("span",{className:"text-xs text-[var(--text)] opacity-50 ml-1",children:["[ctx: ",e.context_length,"]"]})]})})},e.id))):(0,a.jsx)("div",{className:"px-4 py-1.5 text-[var(--text)] opacity-50 text-sm",children:"No models found"})})})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d.$,{size:"default",onClick:()=>{i(!0),t(!1)},variant:"outline",className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4"),children:"Configuration"}),(0,a.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{c(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Chat History"}),(0,a.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{u(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Note System"})]})]}),(0,a.jsx)("div",{className:(0,l.cn)("mt-auto text-center text-[var(--text)] opacity-70 shrink-0 text-xs font-mono pb-4"),children:"Made with ❤️ by @3-Arc"})]})]})]})};var _=s(7086),T=s(5634),z=s(3720),R=s(3732),L=s(6250),P=s(6973);function O(e,t){if("idle"===t)return"Online";if("chat"===e){if("typing"===t)return"Typing…";if("thinking"===t)return"Thinking…"}if("web"===e){if("searching"===t)return"Searching web…";if("thinking"===t)return"Processing SERP…"}if("page"===e){if("reading"===t)return"Reading page…";if("thinking"===t)return"Analyzing…"}return"Online"}const D=({isOpen:e,onClose:t,setSettingsMode:s})=>(0,a.jsx)(_.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(_.Cf,{variant:"themedPanel",className:(0,l.cn)("[&>button]:hidden"),style:{width:"13.75rem",height:"6.875rem",borderRadius:"1.875rem",boxShadow:"0.9375rem 0.9375rem 1.875rem rgb(25, 25, 25), 0 0 1.875rem rgb(60, 60, 60)"},onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsx)(_.c7,{className:"text-center font-['Bruno_Ace_SC'] p-2 header-title-glow",children:(0,a.jsx)(_.L3,{className:"text-base",children:"Welcome"})}),(0,a.jsx)(_.rr,{asChild:!0,children:(0,a.jsxs)("div",{className:"p-4 text-center",children:[(0,a.jsxs)("p",{className:"text-[var(--text)] header-title-glow font-['Bruno_Ace_SC'] mb-2 -mt-7",children:["The game is afoot!",(0,a.jsx)("br",{})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(d.$,{variant:"ghost",className:"fingerprint-pulse-btn",onClick:()=>s(!0),"aria-label":"Connect to your models",children:(0,a.jsx)(R.n4M,{size:"3rem",color:"var(--active)"})})})]})})]})}),I=({children:e})=>(0,a.jsx)("div",{className:(0,l.cn)("inline-block whitespace-nowrap overflow-hidden text-ellipsis w-full max-w-xs","bg-transparent text-[var(--text)]","rounded-md py-0.5","font-['poppins',_sans-serif] text-md text-center font-medium"),children:e}),U=({isOpen:e,onOpenChange:t,config:s,updateConfig:r})=>{const[o,i]=(0,n.useState)(s?.userName||""),[u,m]=(0,n.useState)(s?.userProfile||"");return(0,n.useEffect)((()=>{e&&(i(s?.userName||""),m(s?.userProfile||""))}),[e,s?.userName,s?.userProfile]),(0,a.jsx)(_.lG,{open:e,onOpenChange:t,children:(0,a.jsxs)(_.Cf,{variant:"themedPanel",className:"max-w-xs",children:[(0,a.jsxs)(_.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,a.jsx)(_.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Edit Profile"}),(0,a.jsx)(_.rr,{className:"text-sm text-[var(--text)] opacity-80",children:"Set your display name and profile information. (For chat and export purposes)"})]}),(0,a.jsxs)("div",{className:"px-6 py-5 space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(T.J,{htmlFor:"username",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"Username"}),(0,a.jsx)(y.p,{id:"username",value:o,onChange:e=>i(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)(T.J,{htmlFor:"userprofile",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"User Profile"}),(0,a.jsx)(y.p,{id:"userprofile",value:u,onChange:e=>m(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]})]}),(0,a.jsxs)(_.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,a.jsx)(d.$,{variant:"outline-subtle",size:"sm",onClick:()=>t(!1),children:"Cancel"}),(0,a.jsx)(d.$,{variant:"active-bordered",size:"sm",onClick:()=>{r({userName:o,userProfile:u}),t(!1),c.oR.success("Profile updated!")},children:"Save Changes"})]})]})})},F=({chatTitle:e,settingsMode:t,setSettingsMode:s,historyMode:u,setHistoryMode:m,noteSystemMode:h,setNoteSystemMode:g,deleteAll:p,reset:f,downloadImage:x,downloadJson:v,downloadText:b,downloadMarkdown:w,chatMode:y,chatStatus:N,onAddNewNoteRequest:$})=>{const{config:A,updateConfig:_}=(0,i.UK)(),[T,F]=(0,n.useState)(!1),q=A?.persona||"default",W=M.z[q]||M.z.default,B=e&&!t&&!u&&!h,[V,H]=(0,n.useState)(!1),G=t||u||h,Y=G?"Back to Chat":A?.userName?`Hi ${A.userName}, settings?`:"Settings",J="w-24",K=J,Z="z-50 min-w-[6rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",Q="flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none transition-colors focus:bg-accent focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50";return(0,a.jsx)(j.Bc,{delayDuration:500,children:(0,a.jsxs)("div",{className:(0,l.cn)("border border-[var(--active)]/50","sticky top-0 z-10 p-0"),children:[(0,a.jsxs)("div",{className:"flex items-center h-auto py-0.5 px-2",children:[(0,a.jsxs)("div",{className:(0,l.cn)("flex justify-start items-center min-h-10",J),children:[(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":Y,variant:"ghost",size:G?"sm":void 0,className:(0,l.cn)("text-[var(--text)] rounded-md p-0 h-8 w-8 flex items-center justify-center"),onClick:()=>{G?(s(!1),m(!1),g(!1)):H(!0)},children:G?(0,a.jsx)(r.yGN,{size:"22px"}):(0,a.jsxs)(S,{className:"h-8 w-8 border border-[var(--active)]",children:[(0,a.jsx)(C,{src:W,alt:q}),(0,a.jsx)(k,{children:("default"===q?"C":q.substring(0,1)).toUpperCase()})]})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:Y})]}),!G&&(0,a.jsxs)("div",{className:"flex flex-col justify-center ml-1",children:[(0,a.jsx)("span",{className:"text-[0.8125rem] font-medium text-[var(--text)] leading-tight",children:"default"===q?"Jet":q}),(0,a.jsxs)("span",{className:"text-[0.625rem] text-muted-foreground font-semibold leading-tight flex items-center pt-0.5",children:["idle"===N&&(0,a.jsx)("span",{className:"h-1.5 w-1.5 bg-green-600 rounded-full mr-1"}),O(y,N)]})]})]}),(0,a.jsxs)("div",{className:"flex-grow flex justify-center items-center overflow-hidden px-1",children:[B&&(0,a.jsx)("p",{className:"text-sm font-semibold text-[var(--text)] whitespace-nowrap overflow-hidden text-ellipsis text-center",children:e}),!B&&!u&&!t&&!h&&(0,a.jsx)(I,{children:A?.selectedModel||"No Model Selected"}),t&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("p",{className:"relative top-0 text-lg font-['Bruno_Ace_SC'] header-title-glow",children:"Configuration"})}),u&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("p",{className:"font-['Bruno_Ace_SC'] text-lg header-title-glow",children:"Chat History"})}),h&&(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("p",{className:"font-['Bruno_Ace_SC'] text-lg header-title-glow",children:"Note System"})})]}),(0,a.jsxs)("div",{className:(0,l.cn)("flex justify-end items-center min-h-10",K),children:[!t&&!u&&!h&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Reset Chat",variant:"ghost",size:"sm",className:"text-[var(--text)] hover:bg-black/10 dark:hover:bg-white/10 rounded-md group",onClick:f,children:(0,a.jsx)(o.yPB,{size:"18px",className:"transition-transform duration-300 rotate-0 group-hover:rotate-180 text-[var(--text)]"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Reset Chat"})]}),(0,a.jsxs)(z.bL,{children:[(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(z.l9,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Share Options",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",children:(0,a.jsx)(r.pdY,{size:"18px"})})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Share Options"})]}),(0,a.jsx)(z.ZL,{children:(0,a.jsxs)(z.UC,{className:(0,l.cn)(Z,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-xl"),sideOffset:5,align:"end",children:[(0,a.jsxs)(z.q7,{className:(0,l.cn)(Q,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:()=>F(!0),children:[(0,a.jsx)(R.uSr,{className:"mr-auto h-4 w-4"}),"Edit Profile"]}),(0,a.jsx)(z.wv,{className:(0,l.cn)("-mx-1 my-1 h-px bg-muted","bg-[var(--text)]/10")}),(0,a.jsxs)(z.Pb,{children:[(0,a.jsxs)(z.ZP,{className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent","hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),children:[(0,a.jsx)(r.irw,{className:"mr-auto h-4 w-4"}),"Export Chat"]}),(0,a.jsx)(z.ZL,{children:(0,a.jsxs)(z.G5,{className:(0,l.cn)(Z,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-lg"),sideOffset:2,alignOffset:-5,children:[(0,a.jsxs)(z.q7,{className:(0,l.cn)(Q,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:w,children:[(0,a.jsx)(P.nR3,{className:"mr-auto h-4 w-4"}),".md"]}),(0,a.jsxs)(z.q7,{className:(0,l.cn)(Q,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:b,children:[(0,a.jsx)(R.mup,{className:"mr-auto h-4 w-4"}),".txt"]}),(0,a.jsxs)(z.q7,{className:(0,l.cn)(Q,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:v,children:[(0,a.jsx)(o.dG_,{className:"mr-auto h-4 w-4"}),".json"]}),(0,a.jsxs)(z.q7,{className:(0,l.cn)(Q,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:x,children:[(0,a.jsx)(R.Af8,{className:"mr-auto h-4 w-4"}),".png"]})]})})]})]})})]})]}),u&&(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsx)(d.$,{"aria-label":"Delete All History",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:()=>{c.oR.custom((e=>(0,a.jsxs)("div",{className:(0,l.cn)("bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]","p-4 rounded-lg shadow-xl max-w-sm w-full","flex flex-col space-y-3"),children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-[var(--text)]",children:"Confirm Deletion"}),(0,a.jsx)("p",{className:"text-sm text-[var(--text)] opacity-90",children:"Are you sure you want to delete all chat history? This action cannot be undone."}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-2",children:[(0,a.jsx)(d.$,{variant:"outline",size:"sm",className:(0,l.cn)("bg-transparent text-[var(--text)] border-[var(--text)]","hover:bg-[var(--active)]/30 focus:ring-1 focus:ring-[var(--active)]"),onClick:()=>c.oR.dismiss(e.id),children:"Cancel"}),(0,a.jsx)(d.$,{variant:"destructive",size:"sm",className:(0,l.cn)("focus:ring-1 focus:ring-red-400 focus:ring-offset-1 focus:ring-offset-[var(--bg)]"),onClick:async()=>{try{"function"==typeof p?await p():(console.error("Header: deleteAll prop is not a function or undefined.",p),c.oR.error("Failed to delete history: Operation not available."))}catch(e){console.error("Error during deleteAll execution from header:",e),c.oR.error("An error occurred while deleting history.")}finally{c.oR.dismiss(e.id)}},children:"Delete All"})]})]})),{duration:1/0,position:"top-center"})},children:(0,a.jsx)(r.IXo,{size:"18px"})})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Delete All"})]}),h&&$&&(0,a.jsxs)(j.m_,{children:[(0,a.jsx)(j.k$,{asChild:!0,children:(0,a.jsxs)(d.$,{"aria-label":"Add New Note",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:$,children:[(0,a.jsx)(L.BlJ,{size:"18px"})," "]})}),(0,a.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Add New Note"})]})]})]}),(!A?.models||0===A.models.length)&&!t&&!u&&!h&&(0,a.jsx)(D,{isOpen:!0,setSettingsMode:s,onClose:()=>{}}),(0,a.jsx)(E,{isOpen:V,onOpenChange:e=>{H(e)},config:A,updateConfig:_,setSettingsMode:s,setHistoryMode:m,setNoteSystemMode:g}),(0,a.jsx)(U,{isOpen:T,onOpenChange:F,config:A,updateConfig:_})]})})}},3885:(e,t,s)=>{s.d(t,{Bc:()=>o,ZI:()=>c,k$:()=>l,m_:()=>i});var a=s(4848),n=(s(6540),s(3881)),r=s(5284);function o({delayDuration:e=500,...t}){return(0,a.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,a.jsx)(o,{children:(0,a.jsx)(n.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,a.jsx)(n.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:s,...o}){return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,r.cn)("bg-primary/50 text-primary-foreground border-transparent animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-2 py-1 text-xs text-balance",e),...o,children:[s,(0,a.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-1 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},4273:(e,t,s)=>{s.d(t,{GW:()=>a,hL:()=>i,hj:()=>l,tE:()=>o}),s(6629);const a=async(e,t,s,a,n,r=[],o)=>{try{if(!s?.host)return console.error("processQueryWithAI: currentModel or currentModel.host is undefined. Cannot determine API URL."),e;const i=r.map((e=>`{{${e.role}}}: ${e.content}`)).join("\n"),l=`You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${i}].\n\n\nInstructions:\n**Important** No Explanation, just the optimized query!\n\n\n1. Extract the key keywords and named entities from the user's input.\n2. Correct any obvious spelling errors.\n3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.\n4. If the input is nonsensical or not a query, return the original input.\n5. Using previous chat history to understand the user's intent.\n\n\nOutput:\n'The optimized Google search query'\n\n\nExample 1:\nInput from user ({{user}}): where can i find cheep flights to london\nOutput:\n'cheap flights London'\n\n\nExample 2:\nContext: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!\nInput from user ({{user}}): please choose me the best restarant\nOutput:\n'best restaurants Paris France'\n\n\nExample 3:\nInput from user ({{user}}): asdf;lkjasdf\nOutput:\n'asdf;lkjasdf'\n`,c={ollama:`${t?.ollamaUrl||""}/api/chat`}[s.host];if(!c)return console.error("processQueryWithAI: Could not determine API URL for host:",s.host),e;console.log(`processQueryWithAI: Using API URL: ${c} for host: ${s.host}`),console.log("Formatted Context for Prompt:",i);const d={model:t?.selectedModel||s.id||"",messages:[{role:"system",content:l},{role:"user",content:e}],stream:!1};let u;void 0!==o?u=o:void 0!==t.temperature&&(u=t.temperature),void 0!==u&&(d.temperature=u);const m=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json",...a||{}},signal:n,body:JSON.stringify(d)});if(!m.ok){const e=await m.text();throw console.error(`API request failed with status ${m.status}: ${e}`),new Error(`API request failed: ${m.statusText}`)}const h=await m.json(),g=h?.choices?.[0]?.message?.content;return"string"==typeof g?(e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["']/g,"").trim())(g):e}catch(t){if(n?.aborted||t instanceof Error&&"AbortError"===t.name)throw console.log("processQueryWithAI: Operation aborted."),t;return console.error("processQueryWithAI: Error during execution:",t),e}},n=async function(e){try{const t=new URL(e);if("chrome:"===t.protocol)return;const s=[{id:1,priority:1,condition:{requestDomains:[t.hostname]},action:{type:"modifyHeaders",requestHeaders:[{header:"Origin",operation:"set",value:`${t.protocol}//${t.hostname}`}]}}];await chrome.declarativeNetRequest.updateDynamicRules({removeRuleIds:s.map((e=>e.id)),addRules:s})}catch(e){console.debug("URL rewrite skipped:",e)}},r=e=>{try{const t=(new DOMParser).parseFromString(e,"text/html");t.querySelectorAll('script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]').forEach((e=>e.remove()));let s=t.querySelector("main")||t.querySelector("article")||t.querySelector(".content")||t.querySelector("#content")||t.querySelector(".main-content")||t.querySelector("#main-content")||t.querySelector(".post-content")||t.body,a=s?.textContent||"";return a=a.replace(/\s+/g," ").trim(),a=a.split("\n").filter((e=>e.trim().length>20)).join("\n"),a}catch(e){return console.error("Error parsing HTML for content extraction:",e),"[Error extracting content]"}},o=async(e,t,s)=>{console.log("[webSearch] Received query:",e),console.log("[webSearch] Web Mode from config:",t?.webMode);const a=t.webMode,n=t.serpMaxLinksToVisit??3,o=t.webLimit&&128!==t.webLimit?1e3*t.webLimit:1/0,i=s||(new AbortController).signal;if(console.log(`Performing ${a} search for: "${e}"`),"Google"===a&&console.log(`[webSearch - ${a}] Max links to visit for content scraping: ${n}`),!a)return console.error("[webSearch] Web search mode is undefined. Aborting search. Config was:",JSON.stringify(t)),"Error: Web search mode is undefined. Please check your configuration.";try{if("Google"===a){const t=new AbortController,s=setTimeout((()=>{console.warn(`[webSearch - ${a}] SERP API call timed out after 15s.`),t.abort()}),15e3),l="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i,c=`https://www.google.com/search?q=${encodeURIComponent(e)}&hl=en&gl=us`,d=await fetch(c,{signal:l,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Language":"en-US,en;q=0.9","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","upgrade-insecure-requests":"1",Referer:"https://www.google.com/"}}).finally((()=>{clearTimeout(s)}));if(!d.ok)throw new Error(`Web search failed (${a}) with status: ${d.status}`);if(i.aborted)throw new Error("Web search operation aborted.");const u=await d.text(),m=new DOMParser;console.log(`[webSearch - ${a}] SERP HTML (first 500 chars):`,u.substring(0,500));const h=m.parseFromString(u,"text/html"),g=[];if(h.querySelectorAll("div.g, div.MjjYud, div.hlcw0c").forEach((e=>{const t=e.querySelector("a[href]"),s=t?.getAttribute("href"),a=e.querySelector("h3"),n=a?.textContent?.trim()||"";let r="";const o=e.querySelectorAll('div[style="-webkit-line-clamp:2"], div[data-sncf="1"], .VwiC3b span, .MUxGbd span');if(o.length>0)r=Array.from(o).map((e=>e.textContent)).join(" ").replace(/\s+/g," ").trim();else{const t=e.textContent||"",s=n?t.indexOf(n):-1;-1!==s&&(r=t.substring(s+n.length).replace(/\s+/g," ").trim().substring(0,300))}n&&s&&s.startsWith("http")&&g.push({title:n,snippet:r,url:s})})),console.log(`[webSearch - ${a}] Parsed SERP Results (${g.length} found, showing first 5):`,JSON.stringify(g.slice(0,5))),0===g.length)return console.log("No search results found on SERP."),"No results found.";const p=g.slice(0,n).filter((e=>e.url));console.log(`Found ${g.length} results. Attempting to fetch content from top ${p.length} links (maxLinksToVisit: ${n}).`);const f=p.map((async e=>{if(!e.url)return{...e,content:"[Invalid URL]",status:"error"};if(i.aborted)return{...e,content:`[Fetching aborted by user: ${e.url}]`,status:"aborted"};console.log(`Fetching content from: ${e.url}`);const t=new AbortController,s=setTimeout((()=>{console.warn(`[webSearch] Page scrape for ${e.url} timed out after 12s.`),t.abort()}),12e3),n="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i;let o=`[Error fetching/processing: Unknown error for ${e.url}]`,l="error";try{const t=await fetch(e.url,{signal:n,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9"}});if(!t.ok)throw new Error(`Failed to fetch ${e.url} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e.url}`);if(i.aborted)throw new Error("Web search operation aborted by user.");const c=await t.text();o=r(c),l="success",console.log(`[webSearch - ${a}] Successfully fetched and extracted content from: ${e.url} (Extracted Length: ${o.length})`)}catch(s){if("AbortError"===s.name){if(i.aborted)throw s;o=t.signal.aborted?`[Timeout fetching: ${e.url}]`:`[Fetching aborted: ${e.url}]`,l="aborted"}else o=`[Error fetching/processing: ${s.message}]`,l="error"}finally{clearTimeout(s)}return{...e,content:o,status:l}})),x=await Promise.allSettled(f);if(i.aborted)throw new Error("Web search operation aborted.");let v=`Search results for "${e}" using ${a}:\n\n`,b=0;return g.forEach(((e,t)=>{if(v+=`[Result ${t+1}: ${e.title}]\n`,v+=`URL: ${e.url||"[No URL Found]"}\n`,v+=`Snippet: ${e.snippet||"[No Snippet]"}\n`,t<p.length){const t=x[b];if("fulfilled"===t?.status){const s=t.value;if(s.url===e.url){const e=s.content.substring(0,o);v+=`Content:\n${e}${s.content.length>o?"...":""}\n\n`}else v+=`Content: [Content fetch mismatch - data for ${s.url} found, expected ${e.url}]\n\n`}else v+="rejected"===t?.status?`Content: [Error fetching: ${t.reason}]\n\n`:"Content: [Fetch status unknown]\n\n";b++}else v+="Content: [Not fetched due to link limit]\n\n"})),console.log("Web search finished. Returning combined results."),v.trim()}return`Unsupported web search mode: ${a}`}catch(e){if("AbortError"===e.name&&i.aborted)throw console.log("[webSearch] Operation aborted by signal."),e;return console.error("Web search overall failed:",e),`Error performing web search: ${e.message}`}};async function i(e,t,s,a={},r,o){let i=!1;const l=(e,t=!1)=>{if(!i){let a;i=!0,a="string"==typeof e?e:e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:String(e),s(a,!0,t)}},c=()=>{if(o?.aborted)throw new Error("Streaming operation aborted by user.")};if(e.startsWith("chrome://"))console.log("fetchDataAsStream: Skipping chrome:// URL:",e);else{e.includes("localhost")&&await n((e=>e.endsWith("/")?e.slice(0,-1):e)(e));try{const n=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",...a},body:JSON.stringify(t),signal:o});if(!n.ok){let e=`Network response was not ok (${n.status})`;try{e+=`: ${await n.text()||n.statusText}`}catch(t){e+=`: ${n.statusText}`}throw new Error(e)}let d="";if("ollama"!==r)throw new Error(`Unsupported host specified: ${r}`);{if(!n.body)throw new Error("Response body is null for Ollama");const e=n.body.getReader();let t,a;for(;c(),({value:a,done:t}=await e.read()),!t;){const t=(new TextDecoder).decode(a).split("\n").filter((e=>""!==e.trim()));for(const a of t){if("[DONE]"===a.trim())return o?.aborted&&e.cancel(),void l(d);try{const t=JSON.parse(a);if(t.message?.content&&(d+=t.message.content,i||s(d)),!0===t.done&&!i)return o?.aborted&&e.cancel(),void l(d)}catch(e){console.debug("Skipping invalid JSON chunk:",a)}}}o?.aborted&&e.cancel(),l(d)}}catch(e){o?.aborted?(console.log("[fetchDataAsStream] Operation aborted via signal as expected. Details:",e),l("",!1)):e instanceof Error&&"AbortError"===e.name?(console.log("[fetchDataAsStream] AbortError (name check) caught. Operation was cancelled. Details:",e),l("",!1)):(console.error("Error in fetchDataAsStream (unexpected):",e),l(e instanceof Error?e.message:String(e),!0))}}}async function l(e,t){const s=new AbortController,a=t||s.signal,n=t?null:setTimeout((()=>s.abort()),12e3);try{const t=await fetch(e,{signal:a,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9"}});if(n&&clearTimeout(n),a.aborted)throw new Error("Scraping aborted by user.");if(!t.ok)throw new Error(`Failed to fetch ${e} - Status: ${t.status}`);const s=t.headers.get("content-type");if(!s||!s.includes("text/html"))throw new Error(`Skipping non-HTML content (${s}) from ${e}`);const o=await t.text();return r(o)}catch(t){return n&&clearTimeout(n),"AbortError"===t.name?`[Scraping URL aborted: ${e}]`:`[Error scraping URL: ${e} - ${t.message}]`}}},4539:(e,t,s)=>{s.d(t,{F:()=>o});var a=s(4848),n=(s(6540),s(6627)),r=s(5284);function o({className:e,children:t,viewportRef:s,...o}){return(0,a.jsxs)(n.bL,{"data-slot":"scroll-area",className:(0,r.cn)("relative",e),...o,children:[(0,a.jsx)(n.LM,{ref:s,"data-slot":"scroll-area-viewport",className:(0,r.cn)("size-full rounded-[inherit]","focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:shadow-none","[&>div]:!border-b-0","pb-px pr-px"),children:t}),(0,a.jsx)(i,{orientation:"vertical"}),(0,a.jsx)(i,{orientation:"horizontal"}),(0,a.jsx)(n.OK,{})]})}function i({className:e,orientation:t="vertical",...s}){return(0,a.jsx)(n.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,r.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-px","horizontal"===t&&"h-px w-full border-b-0 bg-transparent shadow-none min-h-0",e),...s,children:(0,a.jsx)(n.lr,{"data-slot":"scroll-area-thumb",className:"relative flex-1 rounded-sm"})})}},5002:(e,t,s)=>{s.d(t,{F2:()=>a,Hg:()=>f,kP:()=>p,m2:()=>v,v0:()=>x,x5:()=>g,xh:()=>h});const a=()=>new Promise((e=>{let t=window.speechSynthesis.getVoices();if(t.length)return void e(t.map((e=>({name:e.name,lang:e.lang}))));const s=()=>{t=window.speechSynthesis.getVoices(),t.length&&(e(t.map((e=>({name:e.name,lang:e.lang})))),window.speechSynthesis.removeEventListener("voiceschanged",s))};window.speechSynthesis.addEventListener("voiceschanged",s)}));let n=null,r=null,o=null,i=null,l=null,c=!1,d="",u=null,m=1;const h=()=>window.speechSynthesis.speaking,g=()=>c||window.speechSynthesis.paused,p=(e,t,s=1,a)=>{(h()||window.speechSynthesis.pending)&&f();const c=new SpeechSynthesisUtterance(e);if(c.rate=s,t){const e=window.speechSynthesis.getVoices().find((e=>e.name===t));e?c.voice=e:console.warn(`Voice "${t}" not found. Using default.`)}o=a?.onStart||null,r=a?.onEnd||null,i=a?.onPause||null,l=a?.onResume||null,c.onstart=()=>{n=c,o&&o()},c.onend=()=>{n===c&&(n=null,r&&r(),o=r=i=l=null)},c.onpause=()=>{n===c&&i&&i()},c.onresume=()=>{n===c&&l&&l()},c.onerror=e=>{console.error("SpeechSynthesisUtterance error:",e.error),n===c&&(n=null,r&&r(),o=r=i=l=null)},window.speechSynthesis.speak(c)},f=()=>{if(!n&&!window.speechSynthesis.speaking&&!window.speechSynthesis.pending)return;const e=r;n=null,d="",u=null,c=!1,o=r=i=l=null,window.speechSynthesis.cancel(),e&&e()},x=()=>{if(n&&h()&&!c)try{c=!0,window.speechSynthesis.pause(),i&&i()}catch(e){console.error("Error pausing speech:",e),n&&(d=n.text,u=n.voice,m=n.rate)}},v=()=>{if(c)try{window.speechSynthesis.resume(),c=!1,l&&l()}catch(e){if(console.error("Error resuming speech, attempting fallback:",e),d&&n){window.speechSynthesis.cancel();const e=new SpeechSynthesisUtterance(d);e.voice=u,e.rate=m,e.onend=n.onend,e.onstart=n.onstart,e.onpause=n.onpause,e.onresume=n.onresume,e.onerror=n.onerror,n=e,window.speechSynthesis.speak(e),c=!1,l&&l()}}}},5095:(e,t,s)=>{s.d(t,{e:()=>o});var a=s(6540),n=s(888),r=s(6948);const o=()=>{const{config:e,updateConfig:t}=(0,r.UK)();return{appendToNote:(0,a.useCallback)((s=>{if(!s||""===s.trim())return void n.oR.error("No text selected to add to note.");const a=e.noteContent||"",r=a+(a&&s.trim()?"\n\n":"")+s.trim();t({noteContent:r}),n.oR.success("Selected text appended to note.")}),[e.noteContent,t])}}},5284:(e,t,s)=>{s.d(t,{cn:()=>r});var a=s(4164),n=s(856);function r(...e){return(0,n.QP)((0,a.$)(e))}},5431:(e,t,s)=>{s.d(t,{A:()=>a});const a={getItem:async e=>{const t=(await chrome.storage.local.get(e))[e];if(null==t)return null;try{return"string"==typeof t?t:JSON.stringify(t)}catch(e){return null}},setItem:async(e,t)=>{const s="string"==typeof t?t:JSON.stringify(t);await chrome.storage.local.set({[e]:s})},deleteItem:async e=>{await chrome.storage.local.remove(e)}}},5634:(e,t,s)=>{s.d(t,{J:()=>o});var a=s(4848),n=(s(6540),s(5920)),r=s(5284);function o({className:e,...t}){return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},5886:(e,t,s)=>{s.d(t,{z2:()=>l});var a=s(38);const n={isLoaded:!1},r=(0,a.Z0)({name:"content",initialState:n,reducers:{reset:()=>n,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:o,reducer:i}=r,l={}},6108:(e,t,s)=>{s.d(t,{z2:()=>d});var a,n,r=s(38);!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(a||(a={})),function(e){e.Default="default"}(n||(n={}));const o={isOpen:!1},i=(0,r.Z0)({name:"sidePanel",initialState:o,reducers:{reset:()=>o}}),{actions:l,reducer:c}=i,d={}},6174:(e,t,s)=>{var a;s.d(t,{A:()=>n}),function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(a||(a={}));const n=a},6508:(e,t,s)=>{s.d(t,{AC:()=>l,Af:()=>c});var a=s(4848),n=s(6540),r=s(3),o=s(2090),i=s(5284);const l=e=>{const{children:t,className:s,wrapperClassName:l,buttonVariant:c="ghost",buttonClassName:d,...u}=e,[m,h]=(0,n.useState)(!1),[g,p]=(0,n.useState)(!1),f=n.Children.only(t);let x="";return f?.props?.children&&(x=Array.isArray(f.props.children)?f.props.children.map((e=>"string"==typeof e?e:"")).join(""):String(f.props.children),x=x.trim()),(0,a.jsxs)("div",{className:(0,i.cn)("relative my-4",l),onMouseEnter:()=>p(!0),onMouseLeave:()=>p(!1),children:[(0,a.jsx)("pre",{className:(0,i.cn)("p-3 rounded-md overflow-x-auto thin-scrollbar",s),...u,children:t}),x&&(0,a.jsx)(o.$,{variant:c,size:"sm","aria-label":m?"Copied!":"Copy code",title:m?"Copied!":"Copy code",className:(0,i.cn)("absolute right-2 top-2 h-8 w-8 p-0","transition-opacity duration-200",g||m?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none",d),onClick:()=>{x&&(navigator.clipboard.writeText(x),h(!0),setTimeout((()=>h(!1)),1500))},children:m?(0,a.jsx)(r.YrT,{className:"h-4 w-4"}):(0,a.jsx)(r.nxz,{className:"h-4 w-4"})})]})},c={ul:({children:e,className:t,...s})=>(0,a.jsx)("ul",{className:(0,i.cn)("list-disc pl-5 my-2",t),...s,children:e}),ol:({children:e,className:t,...s})=>(0,a.jsx)("ol",{className:(0,i.cn)("list-decimal pl-5 my-2",t),...s,children:e}),p:({children:e,className:t,...s})=>(0,a.jsx)("p",{className:(0,i.cn)("mb-0",t),...s,children:e}),pre:l,code:e=>{const{children:t,className:s,inline:n,...r}=e;return n?(0,a.jsx)("code",{className:(0,i.cn)("px-1 py-0.5 rounded-sm bg-[var(--code-inline-bg)] text-[var(--code-inline-text)] text-sm",s),...r,children:t}):(0,a.jsx)("code",{className:(0,i.cn)("font-mono text-sm",s),...r,children:t})},a:({children:e,href:t,className:s,...n})=>(0,a.jsx)("a",{href:t,className:(0,i.cn)("text-[var(--link)] hover:underline",s),target:"_blank",rel:"noopener noreferrer",...n,children:e}),strong:({children:e,className:t,...s})=>(0,a.jsx)("strong",{className:(0,i.cn)("font-bold",t),...s,children:e}),em:({children:e,className:t,...s})=>(0,a.jsx)("em",{className:(0,i.cn)("italic",t),...s,children:e}),h1:({children:e,className:t,...s})=>(0,a.jsx)("h1",{className:(0,i.cn)("text-2xl font-bold mt-4 mb-2 border-b pb-1 border-[var(--border)]",t),...s,children:e}),h2:({children:e,className:t,...s})=>(0,a.jsx)("h2",{className:(0,i.cn)("text-xl font-semibold mt-3 mb-1 border-b pb-1 border-[var(--border)]",t),...s,children:e}),h3:({children:e,className:t,...s})=>(0,a.jsx)("h3",{className:(0,i.cn)("text-lg font-semibold mt-2 mb-1 border-b pb-1 border-[var(--border)]",t),...s,children:e}),table:({children:e,className:t,...s})=>(0,a.jsx)("div",{className:"markdown-table-wrapper my-2 overflow-x-auto",children:(0,a.jsx)("table",{className:(0,i.cn)("w-full border-collapse border border-[var(--border)]",t),...s,children:e})}),thead:({children:e,className:t,...s})=>(0,a.jsx)("thead",{className:(0,i.cn)("bg-[var(--muted)]",t),...s,children:e}),tbody:({children:e,className:t,...s})=>(0,a.jsx)("tbody",{className:(0,i.cn)(t),...s,children:e}),tr:e=>(0,a.jsx)("tr",{className:(0,i.cn)("border-b border-[var(--border)] even:bg-[var(--muted)]/50",e.className),...e}),th:({children:e,className:t,...s})=>(0,a.jsx)("th",{className:(0,i.cn)("p-2 border border-[var(--border)] text-left font-semibold",t),...s,children:e}),td:({children:e,className:t,...s})=>(0,a.jsx)("td",{className:(0,i.cn)("p-2 border border-[var(--border)]",t),...s,children:e}),blockquote:({children:e,className:t,...s})=>(0,a.jsx)("blockquote",{className:(0,i.cn)("pl-4 italic border-l-4 border-[var(--border)] my-2 text-[var(--muted-foreground)]",t),...s,children:e})}},6532:(e,t,s)=>{s.d(t,{p:()=>o});var a=s(4848),n=s(6540),r=s(5284);function o({className:e,type:t,...s}){const[o,i]=n.useState(!1);return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-8 w-full min-w-0 rounded-md bg-transparent px-3 py-1 text-sm transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","border border-[var(--text)]/10 dark:border-0","focus-visible:border-ring","text-[var(--text)] px-2.5","focus:border-[var(--active)] dark:focus:border-0 focus:ring-1 focus:ring-[var(--active)] focus:ring-offset-0","hover:border-[var(--active)] dark:hover:border-0","bg-[var(--input-background)]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive","shadow-[var(--input-base-shadow)]",e,o&&"input-breathing"),onFocus:e=>{i(!0),s.onFocus?.(e)},onBlur:e=>{i(!1),s.onBlur?.(e)},...s})}},6555:(e,t,s)=>{s.d(t,{AM:()=>o,Wv:()=>i,hl:()=>l});var a=s(4848),n=(s(6540),s(9823)),r=s(5284);function o({...e}){return(0,a.jsx)(n.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,a.jsx)(n.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:s=4,...o}){return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"popover-content",align:t,sideOffset:s,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...o})})}},6629:(e,t,s)=>{s.d(t,{s:()=>a});const a=[{value:"chat",label:"Chat"},{value:"page",label:"Page"},{value:"web",label:"Web"}]},6948:(e,t,s)=>{s.d(t,{UK:()=>c,sG:()=>l});var a=s(4848),n=s(6540),r=s(5431);const o=(0,n.createContext)({}),i={personas:{Ein:"You are Ein, a data-savvy academic and research analyst. Your role is to analyze scholarly papers with precision and depth. Behavior: Restate the core problem statements clearly and concisely. Summarize central arguments and key findings, highlighting specific data and factual evidence. Extract primary takeaways and explain their broader implications. Formulate three insightful, text-grounded questions and provide supported answers. Mannerisms: Maintain an analytical, objective tone. Avoid speculation or unsupported claims. Focus on clarity, rigor, and fidelity to the text.",Warren:"You are Warren, a seasoned business analyst focused on long-term strategic insight. Your role is to evaluate markets, business models, and decision-making frameworks. Behavior: Analyze business scenarios methodically. Provide practical, step-by-step strategies with clear ROI potential. Assess risks, opportunity costs, and long-term outcomes. Mannerisms: Use structured, deliberate language. Ask clarifying questions before offering advice. Avoid short-term thinking. Emphasize stability and foresight.",Jet:"You are Jet, a grounded, no-nonsense assistant here to help users solve problems, understand topics, and get things done. Behavior: Be clear, direct, and supportive. Break down complex ideas using analogies and real-life examples. Offer honest feedback without sugarcoating. Mannerisms: Use conversational language. Ask clarifying questions if needed. Prioritize simplicity, clarity, and practical help over politeness or filler.",Agatha:"You are Agatha, a visionary creative who excels at brainstorming and artistic exploration. Your role is to help users generate ideas across writing, art, or unconventional problem-solving. Behavior: Encourage users to think outside the box. Explore imaginative angles and metaphorical framing. Propose unexpected but meaningful concepts. Mannerisms: Use vivid, expressive language. Ask open-ended questions to fuel creativity. Embrace ambiguity and emotional resonance.",Jan:"You are Jan, a sharp-minded strategist skilled in critical thinking, systems design, and logical planning. Your role is to help users break down complex problems and build smart, sustainable solutions. Behavior: Deconstruct challenges into manageable parts. Map dependencies and bottlenecks. Optimize for long-term efficiency and adaptability. Mannerisms: Speak with precision and structure. Use models, frameworks, and scenarios. Always factor in consequences and contingencies.",Sherlock:"You are Sherlock, a master investigator who excels at deduction and root-cause analysis. Your role is to help users uncover hidden patterns, contradictions, and truths. Behavior: Ask targeted questions to challenge assumptions. Trace problems to their source through logical inference. Diagnose with sharp reasoning. Mannerisms: Use formal, clipped language. Think methodically and explain your logic clearly. Focus on getting to the truth, not advising next steps.",Faye:"You are Faye, a sharp-tongued tactician and negotiator who turns pressure into opportunity. Behavior: Break problems into opportunity paths with clear trade-offs. Suggest bold versus safe routes, always with fallback plans. Blend logic with charm, pushing for high-reward plays. Mannerisms: Speak with confidence and dry wit. Use pointed, strategic questions to clarify goals and pressure points. Present options like a gambler: fast, adaptive, and calculated.",Spike:"You are Spike, a capable and versatile executor. Your role is to turn user prompts into actionable results. Behavior: First, correct or clarify the user’s prompt for better accuracy. Add helpful criteria to guide execution. Then, act on the improved prompt as effectively as possible. Mannerisms: Be concise, critical, and sharp. Skip fluff. Use simple, direct language. Focus on feasibility and correctness. When in doubt, fix it and move forward."},generateTitle:!0,backgroundImage:!1,animatedBackground:!1,persona:"Sherlock",webMode:"Google",webLimit:60,serpMaxLinksToVisit:3,contextLimit:60,maxTokens:32480,temperature:.7,topP:.95,presencepenalty:0,models:[],selectedModel:void 0,chatMode:void 0,ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",tts:{selectedVoice:void 0,rate:1},useNote:!1,noteContent:"",userName:"user",userProfile:"",popoverTitleDraft:"",popoverTagsDraft:""},l=({children:e})=>{const[t,s]=(0,n.useState)(i),[l,c]=(0,n.useState)(!0);return(0,n.useEffect)((()=>{(async()=>{try{const e=await r.A.getItem("config"),t=e?JSON.parse(e):i;s(t)}catch(e){console.error("Failed to load config",e),s(i)}finally{c(!1)}})()}),[]),(0,n.useEffect)((()=>{const e=t?.fontSize||i.fontSize;document.documentElement.style.setProperty("font-size",`${e}px`)}),[l,t?.fontSize]),l?(0,a.jsx)("div",{children:"Loading..."}):(0,a.jsx)(o,{value:{config:t,updateConfig:e=>{s((t=>{const s={...t,...e};return r.A.setItem("config",JSON.stringify(s)).catch((e=>console.error("Failed to save config",e))),s}))}},children:e})},c=()=>(0,n.use)(o)},7086:(e,t,s)=>{s.d(t,{Cf:()=>h,Es:()=>p,L3:()=>f,c7:()=>g,lG:()=>d,rr:()=>x});var a=s(4848),n=s(6540),r=s(990),o=s(8697),i=s(5284);const l={default:"bg-black/50",darker:"bg-black/60"},c={default:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",themedPanel:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 duration-200","bg-[var(--bg)] text-[var(--text)] border-[var(--text)]","rounded-lg shadow-xl p-0")};function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e})}function u({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}const m=n.forwardRef((({className:e,variant:t="default",...s},n)=>(0,a.jsx)(r.hJ,{ref:n,"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50",l[t],e),...s})));m.displayName=r.hJ.displayName;const h=n.forwardRef((({className:e,children:t,variant:s="default",...n},l)=>(0,a.jsxs)(u,{"data-slot":"dialog-portal",children:[(0,a.jsx)(m,{variant:"themedPanel"===s?"darker":"default"}),(0,a.jsxs)(r.UC,{ref:l,"data-slot":"dialog-content",className:(0,i.cn)(c[s],e),...n,children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})));function g({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center",e),...t})}function p({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}h.displayName=r.UC.displayName},7334:(e,t,s)=>{s.d(t,{p:()=>k});var a=s(4848),n=s(9018),r=s(3885),o=s(5284),i=s(6629),l=s(6948);const c=()=>{const{config:e,updateConfig:t}=(0,l.UK)(),s=e?.chatMode,c=s||"chat";return(0,a.jsx)(r.Bc,{delayDuration:500,children:(0,a.jsxs)(n.l6,{value:c,onValueChange:e=>{t({chatMode:"chat"===e?void 0:e})},children:[(0,a.jsxs)(r.m_,{children:[(0,a.jsx)(r.k$,{asChild:!0,children:(0,a.jsx)(n.bq,{"aria-label":"Switch Chat Mode",className:(0,o.cn)("border-none shadow-none bg-transparent","hover:bg-[var(--text)]/10","hover:rounded-[8px_0_0_8px]","text-foreground","px-0 pl-2 h-9 w-fit","gap-0","not-focus-visible","[&>svg]:text-[var(--text)]"),children:s?(0,a.jsx)("span",{className:"text-sm font-semibold",children:s}):(0,a.jsx)("span",{className:"text-sm mr-1 font-semibold",children:"Mode"})})}),(0,a.jsx)(r.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,a.jsx)("p",{children:"Switch Chat Mode (Ctrl+M)"})})]}),(0,a.jsx)(n.gC,{align:"end",sideOffset:5,className:(0,o.cn)("bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]/10 font-semibold rounded-md shadow-lg","min-w-[80px] z-50"),children:i.s.map((e=>(0,a.jsx)(n.eb,{value:e.value,className:(0,o.cn)("text-[var(--text)]","hover:brightness-95 focus:bg-[var(--active)] focus:text-[var(--active-foreground)]"),children:e.label},e.value)))})]})})};var d=s(6540),u=s(8027),m=s(6973),h=s(2090),g=s(888),p=s(9696),f=s(6555),x=s(9014);function v({className:e,...t}){return(0,a.jsx)(x.bL,{"data-slot":"switch",className:(0,o.cn)("peer relative inline-flex h-[10px] w-[26px] shrink-0 cursor-pointer items-center rounded-full bg-input transition-colors duration-200 data-[state=checked]:bg-primary data-[state=unchecked]:bg-foreground/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(x.zi,{"data-slot":"switch-thumb",className:(0,o.cn)("pointer-events-none block size-4 rounded-full shadow-md ring-1 transition-transform duration-200 ease-in-out transform","data-[state=checked]:translate-x-[12px] data-[state=checked]:bg-white data-[state=checked]:ring-primary/50","data-[state=unchecked]:translate-x-[0px] data-[state=unchecked]:bg-primary data-[state=unchecked]:ring-primary-foreground/50")})})}var b=s(6532),w=s(5634),y=s(37),j=s(3732),N=s(5002),S=s(2955);const C=()=>{const{config:e,updateConfig:t}=(0,l.UK)(),[s,n]=(0,d.useState)(!1),[i,c]=(0,d.useState)(e.noteContent||""),[u,m]=(0,d.useState)(!1),[x,C]=(0,d.useState)(""),[k,$]=(0,d.useState)("");(0,d.useEffect)((()=>{s?(c(e.noteContent||""),$(e.popoverTitleDraft||""),C(e.popoverTagsDraft||"")):(e.noteContent!==i&&$(""),C(""))}),[s,e]),(0,d.useEffect)((()=>{!s&&u&&((0,N.Hg)(),m(!1)),s&&u&&""===i.trim()&&((0,N.Hg)(),m(!1))}),[s,u,i]);const M=i===(e.noteContent||""),A=k===(e.popoverTitleDraft||""),E=x===(e.popoverTagsDraft||""),_=M&&A&&E,T=!k.trim()&&!i.trim()&&!x.trim(),z=!!k.trim()||!!i.trim()||!!x.trim(),R=!(!e.popoverTitleDraft||!e.popoverTitleDraft.trim())||!(!e.noteContent||!e.noteContent.trim())||!(!e.popoverTagsDraft||!e.popoverTagsDraft.trim()),L=!z&&!R;return(0,a.jsx)(r.Bc,{delayDuration:500,children:(0,a.jsxs)(f.AM,{open:s,onOpenChange:n,children:[(0,a.jsxs)(r.m_,{children:[(0,a.jsx)(r.k$,{asChild:!0,children:(0,a.jsx)(f.Wv,{asChild:!0,children:(0,a.jsx)(h.$,{variant:"ghost",size:"sm",className:(0,o.cn)("rounded-md not-focus-visible",e.useNote?"text-[var(--active)] hover:bg-muted/80":"text-foreground hover:text-foreground hover:bg-[var(--text)]/10"),"aria-label":"Toggle/Edit Note",children:(0,a.jsx)(y.yVo,{})})})}),(0,a.jsx)(r.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,a.jsx)("p",{children:"Toggle/Edit Note"})})]}),(0,a.jsx)(f.hl,{className:"w-[80vw] p-4 bg-[var(--bg)] border-[var(--text)]/10 shadow-lg rounded-md",side:"top",align:"end",sideOffset:5,children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(w.J,{htmlFor:"use-note-switch",className:"text-[var(--text)] font-medium cursor-pointer",children:"Use Note in Chat"}),(0,a.jsx)(v,{id:"use-note-switch",checked:e.useNote||!1,onCheckedChange:e=>{t({useNote:e})}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(b.p,{id:"popover-title-input",type:"text",placeholder:"Title (optional)",value:k,onChange:e=>$(e.target.value),className:"mb-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"}),(0,a.jsx)(p.T,{id:"note-popover-textarea",value:i,onChange:e=>c(e.target.value),placeholder:"Persistent notes for the AI...",className:"mt-1 min-h-[30vh] max-h-[70vh] overflow-y-auto bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] resize-none thin-scrollbar"})]}),(0,a.jsx)("div",{children:(0,a.jsx)(b.p,{id:"popover-tags-input",type:"text",placeholder:"Tags (comma-separated)",value:x,onChange:e=>C(e.target.value),className:"mt-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"})}),(0,a.jsxs)("div",{className:"flex justify-between items-center pt-1",children:[(0,a.jsxs)(r.m_,{children:[(0,a.jsx)(r.k$,{asChild:!0,children:(0,a.jsx)(h.$,{variant:"ghost",size:"sm",className:(0,o.cn)("p-1.5 rounded-md","text-[var(--text)] hover:bg-[var(--text)]/10","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>{i.trim()&&(u?((0,N.Hg)(),m(!1)):((0,N.Hg)(),m(!0),(0,N.kP)(i,e?.tts?.selectedVoice,e?.tts?.rate,{onEnd:()=>{m(!1)}})))},disabled:!i.trim(),"aria-label":u?"Stop reading note":"Read note aloud",children:(0,a.jsx)(y.i0$,{className:"h-5 w-5"})})}),(0,a.jsx)(r.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,a.jsx)("p",{children:u?"Stop Reading":"Read Aloud"})})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(h.$,{variant:"outline",onClick:()=>{c(""),$(""),C(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""}),(0,g.oR)("Note cleared")},disabled:L,className:(0,o.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),children:"Clear"}),(0,a.jsx)(h.$,{variant:"outline",onClick:()=>{t({noteContent:i,popoverTitleDraft:k,popoverTagsDraft:x}),g.oR.success("Draft saved!")},className:(0,o.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),disabled:_,children:"Save"}),(0,a.jsxs)(r.m_,{children:[(0,a.jsx)(r.k$,{asChild:!0,children:(0,a.jsx)(h.$,{variant:"ghost",onClick:async()=>{if(chrome.runtime.sendMessage({type:"SAVE_NOTE_TO_FILE",payload:{content:i}}),g.oR.success("Note saved to file!"),i.trim())try{const e=(new Date).toLocaleString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),s=k.trim()||`Note from Popover - ${e}`,a=""===x.trim()?[]:x.split(",").map((e=>e.trim())).filter((e=>e.length>0));await(0,S.s2)({title:s,content:i,tags:a}),g.oR.success("Snapshot saved to Note System!"),c(""),$(""),C(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""})}catch(e){console.error("Error saving note to system from popover:",e),g.oR.error("Failed to save note to system.")}n(!1)},disabled:T,className:(0,o.cn)("text-xs px-2 py-1 h-auto w-10"),children:(0,a.jsx)(j.Zuq,{size:16})})}),(0,a.jsx)(r.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:"Save to File"})]})]})]})]})})]})})},k=({isLoading:e,message:t,setMessage:s,onSend:n,onStopRequest:i})=>{const{config:f}=(0,l.UK)(),x=(0,d.useRef)(null),[v,b]=(0,d.useState)(!1),[w,y]=(0,d.useState)(!1),j=(0,d.useRef)(s);(0,d.useEffect)((()=>{j.current=s}),[s]),(0,d.useEffect)((()=>{x.current?.focus()}),[t,f?.chatMode]);let N="Type a message...";"web"===f?.chatMode?N="Enter your query...":"page"===f?.chatMode&&(N="Ask about this page...");const S=(0,d.useRef)(null),k=(0,d.useCallback)((async()=>{const e=window.SpeechRecognition||window.webkitSpeechRecognition;if(e)try{await navigator.mediaDevices.getUserMedia({audio:!0});const t=new e;t.lang="en-US",t.continuous=!1,t.interimResults=!1,t.onresult=e=>{const t=Array.from(e.results).map((e=>e[0].transcript)).join("");j.current((e=>e+t))},t.onend=e=>{b(!1),S.current=null},t.onerror=e=>{console.error("Speech recognition error:",e.error);let t="An unknown error occurred.";t="no-speech"===e.error?"No speech was detected. Please try again.":"audio-capture"===e.error?"Audio capture failed. Is the microphone working?":"not-allowed"===e.error?"Microphone access was denied or is blocked.":`Error: ${e.error}`,g.oR.error(`Speech Error: ${t}`,{duration:3e3}),b(!1),S.current=null},S.current&&S.current.stop(),t.start(),S.current=t,b(!0)}catch(e){console.error("Mic access or setup error:",e);let t="Could not access the microphone.";"NotAllowedError"===e.name||e.message?.includes("Permission denied")?t="Please allow microphone access in your browser settings.":"NotFoundError"===e.name&&(t="No microphone found. Please ensure one is connected and enabled."),g.oR.error(`Microphone Error: ${t}`,{duration:3e3}),b(!1)}else g.oR.error("Speech recognition is not supported in this browser.",{duration:3e3})}),[]);(0,d.useEffect)((()=>()=>{S.current&&(S.current.stop(),S.current=null)}),[]);const $="undefined"!=typeof window&&(window.SpeechRecognition||window.webkitSpeechRecognition);return(0,a.jsxs)("div",{className:(0,o.cn)("flex w-full border border-[var(--active)]/50 items-center mb-1 gap-0 p-0 bg-[var(--card,var(--bg-secondary))] rounded-lg shadow-md",w&&"input-breathing"),children:[(0,a.jsx)(c,{}),(0,a.jsx)(p.T,{autosize:!0,ref:x,minRows:1,maxRows:8,autoComplete:"off",id:"user-input",placeholder:N,value:t,autoFocus:!0,onChange:e=>s(e.target.value),onKeyDown:s=>{e||"Enter"!==s.key||!t.trim()||s.altKey||s.metaKey||s.shiftKey||(s.preventDefault(),s.stopPropagation(),n())},className:"flex-grow !bg-transparent p-1 border-none shadow-none outline-none focus-visible:ring-0",onFocus:()=>y(!0),onBlur:()=>y(!1)}),$&&(0,a.jsx)(r.Bc,{delayDuration:500,children:(0,a.jsxs)(r.m_,{children:[(0,a.jsx)(r.k$,{asChild:!0,children:(0,a.jsx)(h.$,{onClick:e=>{e.stopPropagation(),v&&S.current?(S.current.stop(),b(!1)):v||k()},"aria-label":v?"Stop":"Recording",variant:"ghost",size:"sm",className:(0,o.cn)("p-2 mr-1 rounded-md","not-focus",v?"text-red-500 hover:text-red-300 hover:bg-destructive/10":"text-foreground hover:text-foreground hover:bg-[var(--text)]/10"),disabled:e,children:v?(0,a.jsx)(u.g,{size:18}):(0,a.jsx)(m.bj,{size:18})})}),(0,a.jsx)(r.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,a.jsx)("p",{children:v?"Stop":"Recording"})})]})}),(0,a.jsx)(C,{}),(0,a.jsx)(r.Bc,{delayDuration:300,children:(0,a.jsxs)(r.m_,{children:[(0,a.jsx)(r.k$,{asChild:!0,children:(0,a.jsx)(h.$,{"aria-label":"Send",variant:"ghost",size:"sm",className:(0,o.cn)("p-2 ml-1 rounded-md",!e&&"hover:bg-[var(--text)]/10"),onClick:s=>{s.stopPropagation(),e?i():t.trim()&&n()},disabled:!e&&!t.trim(),children:e?(0,a.jsx)(m.wO6,{className:"h-5 w-5 text-foreground"}):(0,a.jsx)(m.B07,{className:"h-5 w-5 text-foreground"})})}),(0,a.jsx)(r.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,a.jsx)("p",{children:e?"Stop":"Send"})})]})})]})}},7520:(e,t,s)=>{s.d(t,{z:()=>a});const a={Agatha:"assets/images/agatha.png",Spike:"assets/images/spike.png",Warren:"assets/images/warren.png",Jet:"assets/images/jet.png",Jan:"assets/images/jan.png",Sherlock:"assets/images/sherlock.png",Ein:"assets/images/ein.png",Faye:"assets/images/faye.png",default:"assets/images/custom.png"}},7660:(e,t,s)=>{s.a(e,(async(e,a)=>{try{s.d(t,{GV:()=>d,ii:()=>u,mR:()=>l,xD:()=>c});var n=s(2506),r=s(5431);const e=()=>(new Date).toJSON().slice(0,19).replace("T","_").replace(/:/g,"-");let o="assistant",i="user";try{const e=await r.A.getItem("config");if(e){const t=JSON.parse(e);t.persona&&"string"==typeof t.persona&&""!==t.persona.trim()&&(o=t.persona),t.userName&&"string"==typeof t.userName&&""!==t.userName.trim()&&(i=t.userName)}}catch(e){console.error("Failed to load config to get persona name for download:",e)}const l=async t=>{if(!t||0===t.length)return;const s=t.map((e=>{let t=`${"assistant"===e.role?o:"user"===e.role?i:e.role}:\n`;return"assistant"===e.role&&e.webDisplayContent&&(t+=`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`),t+=e.rawContent,t})).join("\n\n"),a=document.createElement("a");a.setAttribute("href",`data:text/plain;charset=utf-8,${encodeURIComponent(s)}`);const n=`chat_${e()}.txt`;a.setAttribute("download",n),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)},c=t=>{if(!t||0===t.length)return;const s=t.map((e=>({...e,role:"assistant"===e.role?o:"user"===e.role?i:e.role}))),a={assistantNameInExport:o,userNameInExport:i,chatHistory:s},n=JSON.stringify(a,null,2),r=document.createElement("a");r.setAttribute("href",`data:application/json;charset=utf-8,${encodeURIComponent(n)}`);const l=`chat_${e()}.json`;r.setAttribute("download",l),r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r)},d=t=>{if(!t||0===t.length)return;const s=document.querySelectorAll(".chatMessage");if(!s||0===s.length)return void console.warn("No chat messages found to generate image.");const a=document.createElement("div");if(a.style.display="flex",a.style.flexDirection="column",a.style.paddingBottom="1rem",a.style.background=document.documentElement.style.getPropertyValue("--bg"),s[0]){const e=1.2;a.style.width=s[0].offsetWidth*e+"px"}s.forEach((e=>{const t=e.cloneNode(!0);t instanceof HTMLElement?(t.style.marginTop="1rem",t.style.boxSizing="border-box",a.appendChild(t)):console.warn("Cloned node is not an HTMLElement:",t)})),document.body.appendChild(a),(0,n.$E)(a,{filter:function(e){if(e instanceof Element){const t=e.getAttribute("aria-label");if(t&&["Copy code","Copied!","Save edit","Cancel edit"].includes(t))return!1}return!0},pixelRatio:2,style:{margin:"0",padding:a.style.paddingBottom},backgroundColor:document.documentElement.style.getPropertyValue("--bg")||"#ffffff"}).then((t=>{const s=document.createElement("a");s.setAttribute("href",t);const a=`chat_${e()}.png`;s.setAttribute("download",a),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)})).catch((e=>{console.error("Oops, something went wrong generating the image!",e)})).finally((()=>{document.body.contains(a)&&document.body.removeChild(a)}))},u=t=>{if(!t||0===t.length)return;const s=t.map((e=>{const t=`### ${"assistant"===e.role?o:"user"===e.role?i:e.role}`;let s=e.rawContent;return s=s.replace(/```([\s\S]*?)```/g,"\n```$1```\n"),s=s.replace(/(https?:\/\/[^\s]+)/g,"[Link]($1)"),`${t}\n\n${s}\n`})).join("\n---\n\n"),a=document.createElement("a");a.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(s)}`),a.setAttribute("download",`chat_${e()}.md`),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)};a()}catch(e){a(e)}}),1)},8473:(e,t,s)=>{s.d(t,{D:()=>g});var a=s(4848),n=s(6540),r=s(7211),o=s(2090),i=s(4539),l=s(6250),c=s(3790),d=s.n(c),u=s(6532);const m=e=>new Date(e).toLocaleDateString("sv-SE"),h=12,g=({loadChat:e,onDeleteAll:t,className:s})=>{const[c,g]=(0,n.useState)([]),[p,f]=(0,n.useState)(""),[x,v]=(0,n.useState)(1),[b,w]=(0,n.useState)(null),[y,j]=(0,n.useState)(null),N=(0,n.useCallback)((e=>{const t=e.sort(((e,t)=>t.last_updated-e.last_updated));g(t)}),[]);(0,n.useEffect)((()=>{(async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));if(0===e.length)return g([]),void v(1);const t=e.map((e=>d().getItem(e))),s=(await Promise.all(t)).filter((e=>null!==e&&"object"==typeof e&&"id"in e&&"last_updated"in e));N(s),v(1)}catch(e){console.error("Error fetching messages:",e),g([])}})()}),[N]);const S=(0,n.useMemo)((()=>{if(!p)return c;const e=p.toLowerCase();return c.filter((t=>{const s=t.title?.toLowerCase().includes(e),a=t.turns.some((t=>t.rawContent.toLowerCase().includes(e)));return s||a}))}),[c,p]);(0,n.useEffect)((()=>{v(1)}),[p]);const C=(0,n.useMemo)((()=>Math.max(1,Math.ceil(S.length/h))),[S]);(0,n.useEffect)((()=>{x>C&&v(C)}),[x,C]);const k=(0,n.useMemo)((()=>{const e=(x-1)*h,t=e+h;return S.slice(e,t)}),[S,x]),$=(0,n.useMemo)((()=>k.map((e=>({...e,date:m(e.last_updated)})))),[k]),M=(0,n.useMemo)((()=>Array.from(new Set($.map((e=>e.date))))),[$]),A=(0,n.useCallback)((async e=>{try{await d().removeItem(e);const t=(await d().keys()).filter((e=>e.startsWith("chat_"))),s=(await Promise.all(t.map((e=>d().getItem(e))))).filter((e=>e&&"object"==typeof e&&"id"in e&&"last_updated"in e&&"turns"in e));N(s);const a=s.filter((e=>{if(!p)return!0;const t=p.toLowerCase(),s=e.title?.toLowerCase().includes(t),a=e.turns.some((e=>e.rawContent.toLowerCase().includes(t)));return s||a})),n=Math.max(1,Math.ceil(a.length/h));let r=x;r>n&&(r=n);const o=(r-1)*h;0===a.slice(o,o+h).length&&r>1&&(r-=1),v(r)}catch(e){console.error("Error deleting message:",e)}}),[N,x,p]),E=(0,n.useCallback)((async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));await Promise.all(e.map((e=>d().removeItem(e)))),g([]),t&&t()}catch(e){console.error("Error deleting all messages:",e)}}),[t]);(0,n.useEffect)((()=>(window.deleteAllChats=E,()=>{window.deleteAllChats===E&&delete window.deleteAllChats})),[E]);const _=(0,n.useCallback)((()=>v((e=>Math.min(e+1,C)))),[C]),T=(0,n.useCallback)((()=>v((e=>Math.max(e-1,1)))),[]),z=`flex flex-col w-full ${s||""}`.trim(),R=e=>{f(e.target.value)};return 0!==c.length||p?0===S.length&&p?(0,a.jsxs)("div",{className:z,children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:p,onChange:R,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,a.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,a.jsxs)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:['No results found for "',p,'".']})})]}):(0,a.jsxs)("div",{className:z,children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:p,onChange:R,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,a.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,a.jsx)("div",{className:"px-4 pb-4 font-['Space_Mono',_monospace]",children:M.map((t=>(0,a.jsxs)("div",{className:"mb-3 mt-3",children:[(0,a.jsx)("p",{className:"text-foreground text-lg font-bold overflow-hidden pl-4 pb-1 text-left text-ellipsis whitespace-nowrap w-[90%]",children:t===m(new Date)?"Today":t}),$.filter((e=>e.date===t)).map((t=>(0,a.jsxs)("div",{className:"flex items-center group font-['Space_Mono',_monospace]",onMouseEnter:()=>w(t.id),onMouseLeave:()=>w(null),children:[(0,a.jsxs)("span",{className:"text-foreground text-base font-normal pl-4 w-[4.5rem] flex-shrink-0 font-['Space_Mono',_monospace]",children:[new Date(t.last_updated).getHours().toString().padStart(2,"0"),":",new Date(t.last_updated).getMinutes().toString().padStart(2,"0")]}),(0,a.jsx)("button",{className:`text-foreground text-base font-normal overflow-hidden px-4 py-2 text-left text-ellipsis whitespace-nowrap flex-grow hover:underline hover:underline-offset-4 hover:decoration-1 ${t.id===y?"line-through decoration-2":""} font-['Space_Mono',_monospace]`,onClick:()=>e(t),children:t.title||"Untitled Chat"}),(0,a.jsx)(r.P.div,{className:"shrink-0 transition-opacity duration-150 "+(b===t.id?"opacity-100":"opacity-0 group-hover:opacity-100"),whileHover:{rotate:"15deg"},onMouseEnter:()=>j(t.id),onMouseLeave:()=>j(null),children:(0,a.jsx)(o.$,{variant:"ghost",size:"sm","aria-label":"Delete chat",className:"rounded-full w-8 h-8 font-['Space_Mono',_monospace]",onClick:e=>{e.stopPropagation(),A(t.id)},children:(0,a.jsx)(l.ttk,{className:"h-4 w-4 text-foreground"})})})]},t.id)))]},t)))})}),C>1&&(0,a.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 border-t border-[var(--active)]/50 font-['Space_Mono',_monospace]",children:[(0,a.jsx)(o.$,{onClick:T,disabled:1===x,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,a.jsxs)("span",{className:"text-md",children:["Page ",x," of ",C]}),(0,a.jsx)(o.$,{onClick:_,disabled:x===C,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]})]}):(0,a.jsxs)("div",{className:z,children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:p,onChange:R,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,a.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,a.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,a.jsx)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:"No chat history found."})})]})}},8639:(e,t,s)=>{s.d(t,{B:()=>j});var a=s(4848),n=s(6540),r=s(3),o=s(1319),i=s(9696),l=s(2090),c=s(5284),d=s(8834);function u({...e}){return(0,a.jsx)(d.bL,{"data-slot":"collapsible",...e})}function m({...e}){return(0,a.jsx)(d.R6,{"data-slot":"collapsible-trigger",...e})}function h({...e}){return(0,a.jsx)(d.Ke,{"data-slot":"collapsible-content",...e})}var g=s(1905),p=s(7736),f=s(6948),x=s(6508);const v=({content:e})=>{const[t,s]=(0,n.useState)(!1);return(0,a.jsx)("div",{className:"mb-2",children:(0,a.jsxs)(u,{open:t,onOpenChange:s,className:"w-full",children:[(0,a.jsx)(m,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:(0,c.cn)("mb-1","border-foreground text-foreground hover:text-accent-foreground"),children:t?"Hide Thoughts":"Show Thoughts"})}),(0,a.jsx)(h,{children:(0,a.jsx)("div",{className:(0,c.cn)("p-3 rounded-md border border-dashed","bg-muted","border-muted-foreground","text-muted-foreground"),children:(0,a.jsx)("div",{className:"markdown-body",children:(0,a.jsx)(o.oz,{remarkPlugins:[[g.A,{singleTilde:!1}],p.A],components:b,children:e})})})})]})})},b={...x.Af,pre:e=>(0,a.jsx)(x.AC,{...e,buttonVariant:"copy-button"})},w=({turn:e,index:t,isEditing:s,editText:d,onStartEdit:u,onSetEditText:m,onSaveEdit:h,onCancelEdit:x})=>{const{config:w}=(0,f.UK)(),y=(e.rawContent||"").split(/(<think>[\s\S]*?<\/think>)/g).filter((e=>e&&""!==e.trim())),j=/<think>([\s\S]*?)<\/think>/;return(0,n.useEffect)((()=>{if(!s)return;const e=e=>{"Escape"===e.key?(e.preventDefault(),e.stopPropagation(),x()):"Enter"!==e.key||e.shiftKey||e.altKey||e.metaKey||d.trim()&&(e.preventDefault(),e.stopPropagation(),h())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[s,x,h,d]),(0,a.jsx)("div",{className:(0,c.cn)("border rounded-2xl text-base","w-[calc(100%-2rem)] mx-1 my-2","pb-1 pl-4 pr-4 pt-1","shadow-lg text-left relative","assistant"===e.role?"bg-accent border-[var(--text)]/20":"bg-primary/10 border-[var(--text)]/20","","chatMessage",s?"editing":"",w&&"number"==typeof w.fontSize&&w.fontSize<=15?"font-semibold":""),onDoubleClick:()=>{s||u(t,e.rawContent)},children:s?(0,a.jsxs)("div",{className:"flex flex-col space-y-2 items-stretch w-full p-1",children:[(0,a.jsx)(i.T,{autosize:!0,value:d,onChange:e=>m(e.target.value),placeholder:"Edit your message...",className:(0,c.cn)("w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground","border-input","text-foreground","hover:border-primary focus-visible:border-primary focus-visible:ring-0","min-h-[60px]"),minRows:3,autoFocus:!0}),(0,a.jsxs)("div",{className:"flex font-mono justify-end space-x-2",children:[(0,a.jsxs)(l.$,{size:"sm",variant:"outline",onClick:h,title:"Save changes",children:[(0,a.jsx)(r.YrT,{className:"h-4 w-4 mr-1"})," Save"]}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:x,title:"Discard changes",children:[(0,a.jsx)(r.yGN,{className:"h-4 w-4 mr-1"})," Exit"]})]})]}):(0,a.jsxs)("div",{className:"message-markdown markdown-body relative z-[1] text-foreground",children:["assistant"===e.role&&e.webDisplayContent&&(0,a.jsx)("div",{className:"message-prefix",children:(0,a.jsx)(o.oz,{remarkPlugins:[[g.A,{singleTilde:!1}],p.A],components:b,children:`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`})}),y.map(((e,t)=>{const s=e.match(j);return s&&s[1]?(0,a.jsx)(v,{content:s[1]},`think_${t}`):(0,a.jsx)("div",{className:"message-content",children:(0,a.jsx)(o.oz,{remarkPlugins:[[g.A,{singleTilde:!1}],p.A],components:b,children:e})},`content_${t}`)}))]})})};var y=s(5002);const j=({turns:e=[],isLoading:t=!1,onReload:s=()=>{},settingsMode:o=!1,onEditTurn:i})=>{const[d,u]=(0,n.useState)(-1),[m,h]=(0,n.useState)(null),[g,p]=(0,n.useState)(""),[x,v]=(0,n.useState)(-1),[b,j]=(0,n.useState)(!1),{config:N}=(0,f.UK)(),S=(0,n.useRef)(null),C=(0,n.useRef)(null);(0,n.useEffect)((()=>{const e=setInterval((()=>{const e=(0,y.xh)(),t=(0,y.x5)();e||-1===x?e&&j(t):(v(-1),j(!1))}),250);return()=>clearInterval(e)}),[x]),(0,n.useLayoutEffect)((()=>{const e=C.current;e&&e.scrollHeight-e.scrollTop-e.clientHeight<200&&(e.scrollTop=e.scrollHeight)}),[e]);const k=e=>{navigator.clipboard.writeText(e)},$=()=>{console.log("Handle pause called"),b||(0,y.v0)()},M=()=>{console.log("Handle resume called"),b&&(0,y.m2)()},A=()=>{console.log("Handle stop called"),(0,y.Hg)(),v(-1),j(!1)},E=(e,t)=>{h(e),p(t)},_=()=>{h(null),p("")},T=()=>{null!==m&&g.trim()&&i(m,g),_()};return(0,a.jsxs)("div",{ref:C,id:"messages",className:(0,c.cn)("flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2","no-scrollbar"),style:{background:"var(--bg)",opacity:o?0:1},children:[e.map(((t,n)=>t&&(0,a.jsxs)("div",{className:(0,c.cn)("flex items-start w-full mt-1 mb-1 px-2 relative","user"===t.role?"justify-start":"justify-end"),onMouseEnter:()=>u(n),onMouseLeave:()=>u(-1),children:["assistant"===t.role&&(0,a.jsxs)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 mr-0 pb-3 transition-opacity duration-100",d===n?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:[m!==n&&(0,a.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>k(t.rawContent),title:"Copy message",children:(0,a.jsx)(r.nxz,{className:"text-[var(--text)]"})}),x===n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.$,{"aria-label":b?"Resume":"Pause",variant:"message-action",size:"xs",onClick:b?M:$,title:b?"Resume speech":"Pause speech",children:b?(0,a.jsx)(r.aze,{className:"text-[var(--text)]"}):(0,a.jsx)(r.GHw,{className:"text-[var(--text)]"})}),(0,a.jsx)(l.$,{"aria-label":"Stop",variant:"message-action",size:"xs",onClick:A,title:"Stop speech",children:(0,a.jsx)(r.TG1,{className:"text-[var(--text)]"})})]}),x!==n&&(0,a.jsx)(l.$,{"aria-label":"Speak",variant:"message-action",size:"xs",onClick:()=>((e,t)=>{if(console.log(`Attempting to play index: ${e}`),e===x&&b)return console.log("Attempting to resume paused speech"),void M();const s=(e=>{let t=e;return t=t.replace(/(https?:\/\/[^\s]+)/g,"link"),t=t.replace(/\\([*_{}[\]()#+.!~`-])/g,"$1"),t=t.replace(/(\*\*|__|\*|_)(.*?)\1/g,"$2"),t=t.replace(/~~(.*?)~~/g,"$1"),t=t.replace(/```(?:[\w\-_]+)?\n([\s\S]*?)\n```/g,"$1"),t=t.replace(/`([^`]+)`/g,"$1"),t=t.replace(/^[*+-]\s+/gm,""),t=t.replace(/\*/g,""),t=t.replace(/:/g,"."),t=t.replace(/\//g," "),t=t.replace(/\s{2,}/g," "),t=t.replace(/<[^>]*>/g,""),t=t.replace(/:(?!\.|\s*\d)/g,". "),t=t.replace(/[()[\]{}]/g,""),console.log("cleaned text:",t),t.trim()})(t);console.log(`Starting new speech for index: ${e}`),v(e),(0,y.kP)(s,N?.tts?.selectedVoice,N?.tts?.rate,{onStart:()=>{console.log(`Speech started for index: ${e}`),v(e),j(!1)},onEnd:()=>{console.log(`Speech ended for index: ${e}`),v(-1),j(!1)},onPause:()=>{console.log(`Speech paused for index: ${e}`),j(!0)},onResume:()=>{console.log(`Speech resumed for index: ${e}`),j(!1)}})})(n,t.rawContent),title:"Speak message",children:(0,a.jsx)(r.aze,{className:"text-[var(--text)]"})}),n===e.length-1&&(0,a.jsx)(l.$,{"aria-label":"Reload",variant:"message-action",size:"xs",onClick:s,title:"Reload last prompt",children:(0,a.jsx)(r.jEl,{className:"text-[var(--text)]"})})]}),(0,a.jsx)(w,{turn:t,index:n,isEditing:m===n,editText:g,onStartEdit:E,onSetEditText:p,onSaveEdit:T,onCancelEdit:_}),"user"===t.role&&(0,a.jsx)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 ml-0 pb-1 transition-opacity duration-100",d===n?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:m!==n&&(0,a.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"sm",onClick:()=>k(t.rawContent),title:"Copy message",children:(0,a.jsx)(r.nxz,{className:"text-[var(--text)]"})})})]},t.timestamp||`turn_${n}`))),(0,a.jsx)("div",{ref:S,style:{height:"1px"}})]})}},8698:(e,t,s)=>{s.d(t,{N:()=>r});var a=s(6540),n=s(6948);const r=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),s=(0,a.useRef)(0),r=[{host:"ollama",isEnabled:e=>!!e.ollamaUrl&&!0===e.ollamaConnected,getUrl:e=>`${e.ollamaUrl}/api/tags`,parseFn:(e,t)=>(e?.models??[]).map((e=>({...e,id:e.id??e.name,host:t}))),onFetchFail:(e,t)=>t({ollamaConnected:!1,ollamaUrl:""})}];return{fetchAllModels:(0,a.useCallback)((async()=>{const a=Date.now();if(a-s.current<3e4)return void console.log("[useUpdateModels] Model fetch throttled");s.current=a;const n=e;if(!n)return void console.warn("[useUpdateModels] Config not available, skipping fetch.");console.log("[useUpdateModels] Starting model fetch for all configured services...");const o=await Promise.allSettled(r.map((async e=>{if(!e.isEnabled(n))return{host:e.host,models:[],status:"disabled"};const s=e.getUrl(n);if(!s)return console.warn(`[useUpdateModels] Could not determine URL for host: ${e.host}`),{host:e.host,models:[],status:"error",error:"Invalid URL"};const a=e.getFetchOptions?e.getFetchOptions(n):{},r=await(async(e,t={})=>{try{const s=await fetch(e,t);return s.ok?await s.json():void console.error(`[fetchDataSilently] HTTP error! Status: ${s.status} for URL: ${e}`)}catch(t){return void console.error(`[fetchDataSilently] Fetch or JSON parse error for URL: ${e}`,t)}})(s,a);if(r){const t=e.parseFn(r,e.host);return{host:e.host,models:t,status:"success"}}return e.onFetchFail&&e.onFetchFail(n,t),{host:e.host,models:[],status:"error",error:"Fetch failed"}})));let i=[];o.forEach((e=>{"fulfilled"===e.status&&"success"===e.value.status&&i.push(...e.value.models)}));const l=n.models||[],c={};((e,t)=>{if(e.length!==t.length)return!0;const s=(e,t)=>e.id.localeCompare(t.id),a=[...e].sort(s),n=[...t].sort(s);return JSON.stringify(a)!==JSON.stringify(n)})(i,l)&&(console.log("[useUpdateModels] Aggregated models changed. Updating config."),c.models=i);const d=n.selectedModel,u=c.models||l,m=d&&u.some((e=>e.id===d))?d:u[0]?.id;(m!==d||c.models)&&(c.selectedModel=m),Object.keys(c).length>0?t(c):console.log("[useUpdateModels] No changes to models or selectedModel needed."),console.log("[useUpdateModels] Model fetch cycle complete.")}),[e,t,3e4,r])}}},8971:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(6540),n=s(4273),r=s(5431);const o=1e3;var i=s(1100);try{const e=chrome.runtime.getURL("pdf.worker.mjs");e?i.EA.workerSrc=e:console.error("Failed to get URL for pdf.worker.mjs. PDF parsing might fail.")}catch(e){console.error("Error setting pdf.js worker source:",e)}const l=(e,t,s,l,c,d,u,m,h,g,p)=>{const f=(0,a.useRef)(null),x=(0,a.useRef)(null),v=(e,t,s,a,n)=>{if(f.current===e||s||a||n){if(null===f.current&&null!==e&&(""===t&&s&&!a&&!n||a&&(t.includes("Operation cancelled by user")||t.includes("Streaming operation cancelled"))))return console.log(`[${e}] updateAssistantTurn: Signal received after operation already finalized. Preserving existing state.`),g(!1),void p("idle");d((r=>{if(0===r.length||"assistant"!==r[r.length-1].role){if(console.warn(`[${e}] updateAssistantTurn: No assistant turn found or last turn is not assistant.`),a){const e={role:"assistant",rawContent:`Error: ${t||"Unknown operation error"}`,status:"error",timestamp:Date.now()};return[...r,e]}return r}const o=r[r.length-1],i=!0===a?"error":!0===n?"cancelled":s?"complete":"streaming";let l;if(n){const e=o.rawContent||"";l=e+(e?" ":"")+t}else l=a?`Error: ${t||"Unknown stream/handler error"}`:t;return[...r.slice(0,-1),{...o,rawContent:l,status:i,timestamp:Date.now()}]})),(s||!0===a||!0===n)&&(console.log(`[${e}] updateAssistantTurn: Final state (Finished: ${s}, Error: ${a}, Cancelled: ${n}). Clearing guard and loading.`),g(!1),p(a||n?"idle":"done"),f.current===e&&(f.current=null,x.current&&(x.current=null)))}else console.log(`[${e}] updateAssistantTurn: Guard mismatch (current: ${f.current}), skipping non-final update.`)};return{onSend:async t=>{const a=Date.now();console.log(`[${a}] useSendMessage: onSend triggered.`);const l=t||"";if(!c)return console.log(`[${a}] useSendMessage: Bailing out: Missing config.`),void g(!1);if(!l||!c)return void console.log(`[${a}] useSendMessage: Bailing out: Missing message or config.`);null!==f.current&&(console.warn(`[${a}] useSendMessage: Another send operation (ID: ${f.current}) is already in progress. Aborting previous.`),x.current&&x.current.abort());const b=new AbortController;x.current=b,console.log(`[${a}] useSendMessage: Setting loading true.`),g(!0),m(""),h("");const w=c.chatMode||"chat";p("web"===w?"searching":"page"===w?"reading":"thinking"),f.current=a;const y=l.match(/(https?:\/\/[^\s]+)/g);let j="";if(y&&y.length>0){p("searching");try{j=(await Promise.all(y.map((e=>(0,n.hj)(e,b.signal))))).map(((e,t)=>`Content from [${y[t]}]:\n${e}`)).join("\n\n")}catch(e){j="[Error scraping one or more URLs]"}p("thinking")}const N={role:"user",status:"complete",rawContent:l,timestamp:Date.now()};d((e=>[...e,N])),u(""),console.log(`[${a}] useSendMessage: User turn added to state.`);const S={role:"assistant",rawContent:"",status:"streaming",timestamp:Date.now()+1};d((e=>[...e,S])),console.log(`[${a}] useSendMessage: Assistant placeholder turn added early.`);let C=l,k="",$="";const M="web"===c?.chatMode,A=c?.models?.find((e=>e.id===c.selectedModel));if(!A)return console.error(`[${a}] useSendMessage: No current model found.`),void v(a,"Configuration error: No model selected.",!0,!0);const E=void 0;if(M){console.log(`[${a}] useSendMessage: Optimizing query...`),p("thinking");const e=s.map((e=>({role:e.role,content:e.rawContent})));try{const t=await(0,n.GW)(l,c,A,E,b.signal,e);t&&t.trim()&&t!==l?(C=t,$=`**Optimized query:** "*${C}*"\n\n`,console.log(`[${a}] useSendMessage: Query optimized to: "${C}"`)):($=`**Original query:** "${C}"\n\n`,console.log(`[${a}] useSendMessage: Using original query: "${C}"`))}catch(e){console.error(`[${a}] Query optimization failed:`,e),$=`**Fallback query:** "${C}"\n\n`}}else C=l;if(M){console.log(`[${a}] useSendMessage: Performing web search...`),p("searching");try{if(k=await(0,n.tE)(C,c,b.signal),p("thinking"),b.signal.aborted)return void console.log(`[${a}] Web search was aborted (signal check post-await).`)}catch(e){if(console.error(`[${a}] Web search failed:`,e),"AbortError"===e.name||b.signal.aborted)return void console.log(`[${a}] Web search aborted. onStop handler will finalize UI.`);{k="";const t=`Web Search Failed: ${e instanceof Error?e.message:String(e)}`;return p("idle"),void v(a,t,!0,!0,!1)}}console.log(`[${a}] useSendMessage: Web search completed. Length: ${k.length}`),$&&d((e=>e.map((t=>"assistant"===t.role&&e[e.length-1]===t&&"complete"!==t.status&&"error"!==t.status&&"cancelled"!==t.status?{...t,webDisplayContent:$}:t))))}const _=M?C:l,T=1e3*(c?.webLimit||1),z=T&&"string"==typeof k?k.substring(0,T):k,R=128===c?.webLimit?k:z,L=s.map((e=>({content:e.rawContent||"",role:e.role}))).concat({role:"user",content:l});let P="";if("page"===c?.chatMode){let e="";console.log(`[${a}] useSendMessage: Preparing page content...`),p("reading");try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(t?.url&&!t.url.startsWith("chrome://")){const s=t.url,n=t.mimeType;if(s.toLowerCase().endsWith(".pdf")||n&&"application/pdf"===n){console.log(`[${a}] Detected PDF URL: ${s}. Attempting to extract text.`);try{e=await async function(e,t){try{console.log(`[${t||"PDF"}] Attempting to fetch PDF from URL: ${e}`);const s=await fetch(e);if(!s.ok)throw new Error(`Failed to fetch PDF: ${s.status} ${s.statusText}`);const a=await s.arrayBuffer();console.log(`[${t||"PDF"}] PDF fetched, size: ${a.byteLength} bytes. Parsing...`);const n=await i.YE({data:a}).promise;console.log(`[${t||"PDF"}] PDF parsed. Number of pages: ${n.numPages}`);let r="";for(let e=1;e<=n.numPages;e++){const s=await n.getPage(e);r+=(await s.getTextContent()).items.map((e=>"str"in e?e.str:"")).join(" ")+"\n\n",e%10!=0&&e!==n.numPages||console.log(`[${t||"PDF"}] Extracted text from page ${e}/${n.numPages}`)}return console.log(`[${t||"PDF"}] PDF text extraction complete. Total length: ${r.length}`),r.trim()}catch(s){throw console.error(`[${t||"PDF"}] Error extracting text from PDF (${e}):`,s),s}}(s,a),console.log(`[${a}] Successfully extracted text from PDF. Length: ${e.length}`)}catch(t){console.error(`[${a}] Failed to extract text from PDF ${s}:`,t),e=`Error extracting PDF content: ${t instanceof Error?t.message:"Unknown PDF error"}. Falling back.`}}else console.log(`[${a}] URL is not a PDF. Fetching from storage: ${s}`),e=await r.A.getItem("pagestring")||"",console.log(`[${a}] Retrieved page text content from storage. Length: ${e.length}`)}else console.log(`[${a}] Not fetching page content for URL: ${t?.url} (might be chrome:// or no active tab).`)}catch(t){console.error(`[${a}] Error getting active tab or initial page processing:`,t),e=`Error accessing page content: ${t instanceof Error?t.message:"Unknown error"}`}const t=1e3*(c?.contextLimit||1),s="string"==typeof e?e:"",n=t&&s?s.substring(0,t):s;P=128===c?.contextLimit?s:n,h(P||""),p("thinking"),console.log(`[${a}] Page content prepared for LLM. Length: ${P?.length}`)}else h("");const O=c?.personas?.[c?.persona]||"",D="page"===c?.chatMode&&P?`Use the following page content for context: ${P}`:"",I="web"===c?.chatMode&&R?`Refer to this web search summary: ${R}`:"",U=c?.useNote&&c.noteContent?`Refer to this note for context: ${c.noteContent}`:"";let F="";const q=c.userName?.trim(),W=c.userProfile?.trim();q&&"user"!==q.toLowerCase()&&""!==q?(F=`You are interacting with a user named "${q}".`,W&&(F+=` Their provided profile information is: "${W}".`)):W&&(F=`You are interacting with a user. Their provided profile information is: "${W}".`);const B=[];O&&B.push(O),F&&B.push(F),U&&B.push(U),D&&B.push(D),I&&B.push(I),j&&B.push(`Use the following scraped content from URLs in the user's message:\n${j}`);const V=B.join("\n\n").trim();console.log(`[${a}] useSendMessage: System prompt constructed. Persona: ${!!O}, UserCtx: ${!!F}, NoteCtx: ${!!U}, PageCtx: ${!!D}, WebCtx: ${!!I}, LinkCtx: ${!!j}`);try{if(p("thinking"),"high"===c?.computeLevel&&A)console.log(`[${a}] useSendMessage: Starting HIGH compute level.`),await(async(e,t,s,a,r,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into stages...",!1),d(),await new Promise((e=>setTimeout(e,o)));const u=`You are a planning agent. Given the original task: "${e}", break it down into the main sequential stages required to accomplish it. Output *only* a numbered list of stages. Example:\n1. First stage\n2. Second stage`,m=await(0,n.GW)(u,s,a,r,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("HighCompute - Raw L1 Decomposition Result:",m),i(`Monitoring: Generated Stages:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length)return i("Error: Failed to decompose task into stages. Falling back to direct query.",!0),"Error: Could not decompose task.";const g=[];for(let t=0;t<h.length;t++){const u=h[t];d(),i(`Processing Stage ${t+1}/${h.length}: ${u}...`,!1);const m=`You are a planning agent. Given the stage: "${u}", break it down into the specific sequential steps needed to complete it. Output *only* a numbered list of steps. If no further breakdown is needed, output "No breakdown needed."`;d(),await new Promise((e=>setTimeout(e,o)));const p=await(0,n.GW)(m,s,a,r,l,[],c);console.log(`HighCompute - Raw L2 Decomposition Result (Stage ${t+1}):`,p);const f=p.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));i(`Monitoring: Generated Steps for Stage ${t+1}:\n${f.join("\n")||"[None, or direct solve]"}`,!1);let x="";if(0===f.length||p.includes("No breakdown needed")){i(`Solving Stage ${t+1} directly...`,!1),d();const c=`Complete the following stage based on the original task "${e}": "${u}"`;d(),await new Promise((e=>setTimeout(e,o))),x=await(0,n.GW)(c,s,a,r,l),console.log(`HighCompute - Raw Direct Solve Result (Stage ${t+1}):`,x),i(`Monitoring: Direct Solve Result for Stage ${t+1}:\n${x}`,!1)}else{const m=[],h=2;let g="";for(let c=0;c<f.length;c+=h){const p=f.slice(c,c+h),x=c/h+1;d(),i(`Solving Step Batch ${x} for Stage ${t+1}: ${p.join(", ")}...`,!1);const v=`You are an expert problem solver. Given the stage: "${u}" and the original task: "${e}", complete the following steps.  Consider the following accumulated context from previous steps: ${g}\n\n${p.map(((e,t)=>`${c+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the steps.`;d(),await new Promise((e=>setTimeout(e,o)));const b=await(0,n.GW)(v,s,a,r,l);console.log(`HighCompute - Raw Batch Results (Stage ${t+1}, Batch ${x}):`,b),i(`Monitoring: Raw Batch Results for Stage ${t+1}, Batch ${x}:\n${b}`,!1);const w=b.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`HighCompute - Parsed Batch Results (Stage ${t+1}, Batch ${x}):`,w),i(`Monitoring: Parsed Batch Results for Stage ${t+1}, Batch ${x}:\n${w.join("\n")||"[None]"}`,!1);for(let e=0;e<w.length;e++){const t=w[e];m.push(t),g+=`Step ${c+e+1}: ${t}\n`}}i(`Synthesizing results for Stage ${t+1}...`,!1),d(),await new Promise((e=>setTimeout(e,o)));const p=`Synthesize the results of the following steps for stage "${u}" into a coherent paragraph:\n\n${m.map(((e,t)=>`Step ${t+1} Result:\n${e}`)).join("\n\n")}`;x=await(0,n.GW)(p,s,a,r,l,[],c),console.log(`HighCompute - Raw Stage Synthesis Result (Stage ${t+1}):`,x),i(`Monitoring: Synthesized Result for Stage ${t+1}:\n${x}`,!1)}g.push(x),i(`Monitoring: Accumulated Stage Results so far:\n${g.map(((e,t)=>`Stage ${t+1}: ${e}`)).join("\n---\n")}`,!1)}i("Synthesizing final answer...",!1),d();const p=`Based on the results of the following stages, provide a final comprehensive answer for the original task "${e}":\n\n${g.map(((e,t)=>`Stage ${t+1} (${h[t]}):\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${p}`,!1),console.log("HighCompute - Final Synthesis Prompt:",p),d(),await new Promise((e=>setTimeout(e,o)));const f=await(0,n.GW)(p,s,a,r,l,[],c);console.log("HighCompute - Raw Final Synthesis Result:",f);const x="**High Compute Breakdown:**\n\n"+g.map(((e,t)=>`**Stage ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(_,0,c,A,E,((e,t)=>v(a,e,Boolean(t))),b.signal),console.log(`[${a}] useSendMessage: HIGH compute level finished.`);else if("medium"===c?.computeLevel&&A)console.log(`[${a}] useSendMessage: Starting MEDIUM compute level.`),await(async(e,t,s,a,r,i,l)=>{const c=Math.max(.1,.5*(s.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into subtasks...",!1),d(),await new Promise((e=>setTimeout(e,o)));const u=`You are a planning agent. Given the task: "${e}", break it down into logical subtasks needed to accomplish it. Output *only* a numbered list of subtasks.`,m=await(0,n.GW)(u,s,a,r,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("MediumCompute - Raw Decomposition Result:",m),i(`Monitoring: Generated Subtasks:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length){i("Warning: Failed to decompose into subtasks. Attempting direct query.",!1),d(),await new Promise((e=>setTimeout(e,o)));const t=await(0,n.GW)(e,s,a,r,l);return i(t,!0),t}const g=[];for(let t=0;t<h.length;t+=2){const c=h.slice(t,t+2),u=t/2+1;d(),i(`Solving Subtask Batch ${u}: ${c.join(", ")}...`,!1);const m=`You are an expert problem solver. Given the task: "${e}", complete the following subtasks:\n\n${c.map(((e,s)=>`${t+s+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the subtasks.`;d(),await new Promise((e=>setTimeout(e,o)));const p=await(0,n.GW)(m,s,a,r,l);console.log(`MediumCompute - Raw Batch Results (Batch ${u}):`,p),i(`Monitoring: Raw Batch Results for Batch ${u}:\n${p}`,!1);const f=p.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`MediumCompute - Parsed Batch Results (Batch ${u}):`,f),i(`Monitoring: Parsed Batch Results for Batch ${u}:\n${f.join("\n")||"[None]"}`,!1);for(let e=0;e<f.length;e++)g.push(f[e])}i("Synthesizing final answer...",!1),d(),await new Promise((e=>setTimeout(e,o)));const p=`Synthesize the results of the following subtasks into a final comprehensive answer for the original task "${e}":\n\n${g.map(((e,t)=>`Subtask ${t+1} Result:\n${e}`)).join("\n\n")}`;console.log("MediumCompute - Final Synthesis Prompt:",p),i(`Monitoring: Final Synthesis Prompt:\n${p}`,!1);const f=await(0,n.GW)(p,s,a,r,l,[],c);console.log("MediumCompute - Raw Final Synthesis Result:",f);const x="**Medium Compute Breakdown:**\n\n"+g.map(((e,t)=>`**Subtask ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(_,0,c,A,E,((e,t)=>v(a,e,Boolean(t))),b.signal),console.log(`[${a}] useSendMessage: MEDIUM compute level finished.`);else{console.log(`[${a}] useSendMessage: Starting standard streaming.`);const e={stream:!0},t={ollama:`${c?.ollamaUrl||""}/api/chat`}[A.host||""];if(!t)return void v(a,`Configuration error: Could not determine API URL for host '${A.host}'.`,!0,!0);const s=[];""!==V.trim()&&s.push({role:"system",content:V}),s.push(...L),console.log(`[${a}] useSendMessage: Sending chat request to ${t} with system prompt: "${V}"`),await(0,n.hL)(t,{...e,model:c?.selectedModel||"",messages:s,temperature:c?.temperature??.7,max_tokens:c?.maxTokens??32048,top_p:c?.topP??1,presence_penalty:c?.presencepenalty??0},((e,t,s)=>{v(a,e,Boolean(t),Boolean(s)),(t||s)&&console.log(`[${a}] fetchDataAsStream Callback: Stream finished/errored.`)}),E,A.host||"",b.signal),console.log(`[${a}] useSendMessage: fetchDataAsStream call INITIATED.`)}}catch(t){if(b.signal.aborted)console.log(`[${a}] Send operation was aborted. 'onStop' handler is responsible for UI updates.`),e&&g(!1),p("idle"),f.current===a&&(f.current=null),x.current&&x.current.signal===b.signal&&(x.current=null);else{console.error(`[${a}] useSendMessage: Error during send operation:`,t);const e=t instanceof Error?t.message:String(t);v(a,e,!0,!0)}}console.log(`[${a}] useSendMessage: onSend processing logic completed.`)},onStop:()=>{const e=f.current;null!==e?(console.log(`[${e}] useSendMessage: onStop triggered.`),x.current&&(x.current.abort(),x.current=null),v(e,"[Operation cancelled by user]",!0,!1,!0)):(console.log("[No CallID] useSendMessage: onStop triggered but no operation in progress."),g(!1),p("idle"))}}}},9018:(e,t,s)=>{s.d(t,{bq:()=>g,eb:()=>f,gC:()=>p,l6:()=>m,yv:()=>h});var a=s(4848),n=s(6540),r=s(854),o=s(5107),i=s(5773),l=s(2102),c=s(5284);const d={default:"bg-transparent data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs data-[size=sm]:h-8",settingsPanel:(0,c.cn)("text-[var(--text)] rounded-xl shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--input-background)]","border-[var(--text)]/10","h-8"),settings:(0,c.cn)("text-[var(--text)] rounded-md shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--input-background)]","border-[var(--text)]/10","h-8")},u={default:"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",settingsPanel:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto","bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]/10","rounded-md shadow-lg")};function m({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function h({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}const g=n.forwardRef((({className:e,size:t="default",variant:s="default",children:n,...i},l)=>(0,a.jsxs)(r.l9,{ref:l,"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("flex w-fit items-center justify-between gap-2 rounded-md border px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",d[s],e),...i,children:[n,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"size-4 opacity-50"})})]})));g.displayName=r.l9.displayName;const p=n.forwardRef((({className:e,children:t,position:s="popper",variant:n="default",...o},i)=>(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:i,"data-slot":"select-content",className:(0,c.cn)(u[n],"default"===n&&"popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...o,children:[(0,a.jsx)(x,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(v,{})]})})));function f({className:e,children:t,focusVariant:s="default",...n}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("[&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2","activeTheme"===s?"text-[var(--text)] hover:brightness-95 focus:bg-[var(--active)] focus:text-[var(--text)]":"text-popover-foreground focus:bg-accent focus:text-accent-foreground",e),...n,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function v({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}p.displayName=r.UC.displayName},9696:(e,t,s)=>{s.d(t,{T:()=>o});var a=s(4848),n=(s(6540),s(1663)),r=s(5284);function o({className:e,autosize:t=!1,minRows:s,maxRows:o,style:i,...l}){return t?(0,a.jsx)(n.A,{minRows:s,maxRows:o,style:i,className:(0,r.cn)("flex w-full bg-transparent placeholder:text-muted-foreground","focus-visible:border-ring focus-visible:ring-ring/50","field-sizing-content text-sm md:text-sm transition-[color,box-shadow] outline-none focus-visible:ring-[3px]","disabled:cursor-not-allowed disabled:opacity-50","thin-scrollbar",e),...l}):(0,a.jsx)("textarea",{"data-slot":"textarea-default",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...l})}},9853:(e,t,s)=>{s.d(t,{S:()=>i});var a=s(6540),n=s(6948),r=s(4273);const o=e=>{const t=e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/"/g,"").replace(/#/g,"").trim();return t&&t.split(/\s+/).slice(0,4).join(" ")||"New Chat"},i=(e,t,s)=>{const[i,l]=(0,a.useState)(""),{config:c}=(0,n.UK)(),d=(0,a.useRef)(null);return(0,a.useEffect)((()=>{if(!e&&t.length>=2&&!i&&c?.generateTitle){d.current&&d.current.abort(),d.current=new AbortController;const e=d.current.signal,s=c?.models?.find((e=>e.id===c.selectedModel));if(!s)return;const a=[...t.slice(0,2).map((e=>({content:e.rawContent||"",role:e.role}))),{role:"user",content:"Create a short 2-4 word title for this chat. Keep it concise, just give me the best one, just one. No explanations or thinking steps needed."}],n=(()=>{const e={body:{model:s.id,messages:a,stream:!["ollama","lmStudio"].includes(s.host||"")},headers:{}};if("ollama"===s.host)return{...e,url:`${c.ollamaUrl}/api/chat`}})();if(!n)return;const i=t=>{e.aborted?console.log("Title generation aborted."):console.error("Title generation failed:",t)};if(["ollama"].includes(s.host||""))fetch(n.url,{method:"POST",headers:{"Content-Type":"application/json",...n.headers},body:JSON.stringify(n.body),signal:e}).then((e=>e.json())).then((e=>{const t=e.choices?.[0]?.message?.content||"",s=o(t);s&&(console.log("Setting chat title (local):",s),l(s))})).catch(i);else{let t="";(0,r.hL)(n.url,n.body,((s,a)=>{if(t=s,e.aborted)console.log("Title streaming aborted during callback.");else if(a){const e=o(t);e&&(console.log("Setting chat title (streaming):",e),l(e))}}),n.headers,s.host||"",e)}}return()=>{d.current&&(d.current.abort(),d.current=null)}}),[e,t,s,c,i]),{chatTitle:i,setChatTitle:l}}}},l={};function c(e){var t=l[e];if(void 0!==t)return t.exports;var s=l[e]={exports:{}};return i[e].call(s.exports,s,s.exports,c),s.exports}c.m=i,e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",s="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",a=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},c.a=(n,r,o)=>{var i;o&&((i=[]).d=-1);var l,c,d,u=new Set,m=n.exports,h=new Promise(((e,t)=>{d=t,c=e}));h[t]=m,h[e]=e=>(i&&e(i),u.forEach(e),h.catch((e=>{}))),n.exports=h,r((n=>{var r;l=(n=>n.map((n=>{if(null!==n&&"object"==typeof n){if(n[e])return n;if(n.then){var r=[];r.d=0,n.then((e=>{o[t]=e,a(r)}),(e=>{o[s]=e,a(r)}));var o={};return o[e]=e=>e(r),o}}var i={};return i[e]=e=>{},i[t]=n,i})))(n);var o=()=>l.map((e=>{if(e[s])throw e[s];return e[t]})),c=new Promise((t=>{(r=()=>t(o)).r=0;var s=e=>e!==i&&!u.has(e)&&(u.add(e),e&&!e.d&&(r.r++,e.push(r)));l.map((t=>t[e](s)))}));return r.r?c:o()}),(e=>(e?d(h[s]=e):c(m),a(i)))),i&&i.d<0&&(i.d=0)},n=[],c.O=(e,t,s,a)=>{if(!t){var r=1/0;for(d=0;d<n.length;d++){for(var[t,s,a]=n[d],o=!0,i=0;i<t.length;i++)(!1&a||r>=a)&&Object.keys(c.O).every((e=>c.O[e](t[i])))?t.splice(i--,1):(o=!1,a<r&&(r=a));if(o){n.splice(d--,1);var l=s();void 0!==l&&(e=l)}}return e}a=a||0;for(var d=n.length;d>0&&n[d-1][2]>a;d--)n[d]=n[d-1];n[d]=[t,s,a]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var s=Object.create(null);c.r(s);var a={};r=r||[null,o({}),o([]),o(o)];for(var n=2&t&&e;"object"==typeof n&&!~r.indexOf(n);n=o(n))Object.getOwnPropertyNames(n).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,c.d(s,a),s},c.d=(e,t)=>{for(var s in t)c.o(t,s)&&!c.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=524,(()=>{var e={524:0};c.O.j=t=>0===e[t];var t=(t,s)=>{var a,n,[r,o,i]=s,l=0;if(r.some((t=>0!==e[t]))){for(a in o)c.o(o,a)&&(c.m[a]=o[a]);if(i)var d=i(c)}for(t&&t(s);l<r.length;l++)n=r[l],c.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return c.O(d)},s=self.webpackChunkcognito=self.webpackChunkcognito||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))})(),c.nc=void 0;var d=c.O(void 0,[465],(()=>c(3003)));d=c.O(d)})();