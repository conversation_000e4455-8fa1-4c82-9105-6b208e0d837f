(()=>{"use strict";var e,t,o,n={1541:(e,t,o)=>{var n=o(38);const r={isLoaded:!1},i=(0,n.Z0)({name:"content",initialState:r,reducers:{reset:()=>r,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:c,reducer:l}=i;var a,s,d=o(9448),u=o(7346),f=o(3207);!function(e){e.Default="default",e.Confirm<PERSON>eleteCard="confirmDeleteCard"}(a||(a={})),function(e){e.Default="default"}(s||(s={}));const p={isOpen:!1},g=(0,n.Z0)({name:"sidePanel",initialState:p,reducers:{reset:()=>p}}),{actions:y,reducer:C}=g,v={},m=((0,f.nK)(v),u.P,(0,n.N0)(),d.logger,[(0,f.nK)(v),u.P,(0,n.N0)(),d.logger]);var h;(0,f.nK)(v),d.logger,function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(h||(h={}));const b=h;var S=o(609),O=o.n(S),w=o(7321),P=o(8431);(async()=>{try{if("chrome:"===window.location.protocol||"chrome-extension:"===window.location.protocol)return;console.log("[Cognito Content Script] Initializing...");const e=(e=>{const t=new f.il({channelName:e});return(0,f.Tw)(t,...m),t})(b.ContentPort);console.log("[Cognito Content Script] Store proxy created.");try{await e.ready(),console.log("[Cognito Content Script] Store ready."),e.dispatch((async(e,t)=>{const{isLoaded:o}=t().content||{};o||await e(i.actions.contentLoaded())})),console.log("[Cognito Content Script] contentLoaded dispatched.")}catch(e){console.error("Cognito - Content script store init error:",e)}}catch(e){console.error("Cognito - Content script main initialization error:",e)}})(),chrome.runtime.onMessage.addListener(((e,t,o)=>{if("DEFUDDLE_PAGE_CONTENT"===e.type){let e=null;console.log("[Cognito Content Script] Received DEFUDDLE_PAGE_CONTENT request for URL:",document.location.href);try{if("application/pdf"===document.contentType)return o({success:!1,error:"Cannot defuddle PDF content directly. Please save or copy text manually.",title:document.title||"PDF Document"}),!0;if(void 0===O())return console.error("[Cognito Content Script] Defuddle library is undefined. Make sure it is imported and bundled correctly."),o({success:!1,error:"Defuddle library not available.",title:document.title}),!0;console.log("[Cognito Content Script] Defuddle library seems available. Type:",typeof O());const t=new(O())(document,{markdown:!1,url:document.location.href});console.log("[Cognito Content Script] Defuddle instance created. Starting parse...");const n=t.parse();if(console.log("[Cognito Content Script] Defuddle HTML parse complete. Title:",n.title,"HTML Content length:",n.content?.length),void 0===P.A)return console.error("[Cognito Content Script] TurndownService library is undefined. Make sure it is imported and bundled correctly."),o({success:!1,error:"TurndownService library not available.",title:document.title}),!0;e=new P.A({headingStyle:"atx",codeBlockStyle:"fenced"}).use(w.Te);const r=e.turndown(n.content||"");console.log("[Cognito Content Script] Turndown conversion complete. Markdown length:",r?.length);const i=document.querySelector("h1, h2, h3")?.textContent?.trim(),c=document.title||"Untitled Note";o({success:!0,title:i||n.title||c,content:r}),console.log("[Cognito Content Script] Sent successful defuddle response to background.")}catch(e){console.error("[Cognito Content Script] Error running Defuddle:",e,e.stack),o({success:!1,error:e.message,title:document.title})}return!0}}))}},r={};function i(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return n[e].call(o.exports,o,o.exports,i),o.exports}i.m=n,e=[],i.O=(t,o,n,r)=>{if(!o){var c=1/0;for(d=0;d<e.length;d++){for(var[o,n,r]=e[d],l=!0,a=0;a<o.length;a++)(!1&r||c>=r)&&Object.keys(i.O).every((e=>i.O[e](o[a])))?o.splice(a--,1):(l=!1,r<c&&(c=r));if(l){e.splice(d--,1);var s=n();void 0!==s&&(t=s)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[o,n,r]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,n){if(1&n&&(e=this(e)),8&n)return e;if("object"==typeof e&&e){if(4&n&&e.__esModule)return e;if(16&n&&"function"==typeof e.then)return e}var r=Object.create(null);i.r(r);var c={};t=t||[null,o({}),o([]),o(o)];for(var l=2&n&&e;"object"==typeof l&&!~t.indexOf(l);l=o(l))Object.getOwnPropertyNames(l).forEach((t=>c[t]=()=>e[t]));return c.default=()=>e,i.d(r,c),r},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=854,(()=>{var e={854:0};i.O.j=t=>0===e[t];var t=(t,o)=>{var n,r,[c,l,a]=o,s=0;if(c.some((t=>0!==e[t]))){for(n in l)i.o(l,n)&&(i.m[n]=l[n]);if(a)var d=a(i)}for(t&&t(o);s<c.length;s++)r=c[s],i.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return i.O(d)},o=self.webpackChunkcognito=self.webpackChunkcognito||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})(),i.nc=void 0;var c=i.O(void 0,[465],(()=>i(1541)));c=i.O(c)})();