export interface Persona {
  Ein: string;
  Jan: string;
  Spike: string;
  Sherlock: string;
  Agatha: string;
  Jet: string;
  Warren: string;
}

export interface Model {
  id: string;
  host?: 'ollama' | string;
  active?: boolean;
  context_length?: number;
  name?: string;
}

export interface TtsSettings {
  selectedVoice?: string;
  rate?:number;
  pitch?:number;
  volume?:number;
}

export const CHAT_MODE_OPTIONS = [
  { value: "chat", label: "Chat" },
  { value: "page", label: "Page" },
  { value: "web", label: "Web" },
] as const;

export type ChatMode = typeof CHAT_MODE_OPTIONS[number]['value'];

export type ChatStatus =
  | 'idle'
  | 'typing'
  | 'searching'
  | 'reading'
  | 'thinking'
  | 'done';
export interface Config {
  personas: Record<string, string>;
  persona: string;
  generateTitle?: boolean;
  backgroundImage?: boolean;
  animatedBackground?: boolean;
  webMode?: 'Google' | string;
  webLimit?: number;
  serpMaxLinksToVisit?: number;
  contextLimit: number;
  ModelSettingsPanel?: Record<string, unknown>;
  temperature: number;
  maxTokens: number;
  topP: number;
  presencepenalty: number;
  ollamaUrl?: string;
  ollamaConnected?: boolean;
  ollamaError?: string | unknown;
  visibleApiKeys?: boolean;
  fontSize?: number;
  models?: Model[];
  selectedModel?: string;
  useNote?: boolean;
  noteContent?: string;
  chatMode?: Exclude<ChatMode, 'chat'>;
  computeLevel: 'low' | 'medium' | 'high' | string;
  panelOpen: boolean;
  tts?: TtsSettings;
  userName?: string;
  userProfile?: string;
  popoverTitleDraft?: string;
  popoverTagsDraft?: string;
}

export interface ConfigContextType {
  config: Config;
  updateConfig: (newConfig: Partial<Config>) => void;
}