import { useEffect, useState } from 'react';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input'; 
import { Slider } from '@/components/ui/slider';
import { cn } from "@/src/background/util";
import { Checkbox } from "@/components/ui/checkbox";

import { useConfig } from './ConfigContext';
import type { Config } from '../types/config';
import { SettingTitle } from './SettingsTitle';

interface WebSearchModeSelectorProps {
  webMode: Config['webMode'];
  updateConfig: (newConfig: Partial<Config>) => void;
}

const WebSearchModeSelector = ({ webMode, updateConfig }: WebSearchModeSelectorProps) => (
  <RadioGroup
    value={webMode}
    onValueChange={(value) => updateConfig({ webMode: value as Config['webMode'] })}
    className="w-1/2 space-y-3"
  >
    {['Google'].map(mode => (
      <div key={mode} className="flex items-center space-x-2">
        <RadioGroupItem
          value={mode}
          id={`webMode-${mode}`}
          variant="themed"
        />
        <Label
          htmlFor={`webMode-${mode}`}
          className="text-[var(--text)] text-base font-medium cursor-pointer"
        >
          {mode}
        </Label>
      </div>
    ))}
  </RadioGroup>
);

interface SerpSettingsPanelProps {
  config: Config;
  updateConfig: (newConfig: Partial<Config>) => void;
}

const SerpSettingsPanel = ({ config, updateConfig }: SerpSettingsPanelProps) => {
  const charLimit = config?.webLimit ?? 16; 
  const maxLinks = config?.serpMaxLinksToVisit ?? 3;

  return (
    <div className="w-full space-y-4">
      <div>
        <p className="text-[var(--text)] text-base font-medium pb-2 text-left">
          Max Links to Visit: <span className="font-normal">{maxLinks}</span>
        </p>
        <Slider
          value={[maxLinks]}
          max={10}
          min={1}
          step={1}
          variant="themed"
          onValueChange={value => updateConfig({ serpMaxLinksToVisit: value[0] })}
        />
        <p className="text-[var(--text)]/70 text-xs pt-1">
          Number of search result links to fetch.
        </p>
      </div>

      <div className="pt-2">
        <p className="text-[var(--text)] text-base font-medium pb-2 text-left">
          Content Char Limit:{' '}
          <span className="font-normal">{charLimit === 128 ? 'Unlimited (Full)' : `${charLimit}k`}</span>
        </p>
        <Slider
          value={[charLimit]}
          max={128} 
          min={1}   
          step={1}
          variant="themed"
          onValueChange={value => updateConfig({ webLimit: value[0] })}
        />
         <p className="text-[var(--text)]/70 text-xs pt-1">
          Max characters (in thousands) of content to use. 128k for 'Unlimited'.
        </p>
      </div>
    </div>
  );
};




export const WebSearch = () => {
  const { config, updateConfig } = useConfig();

  useEffect(() => {
    if (config?.webMode === 'Google') {
      const updates: Partial<Config> = {};
      if (typeof config?.serpMaxLinksToVisit === 'undefined') updates.serpMaxLinksToVisit = 3;
      if (typeof config?.webLimit === 'undefined') updates.webLimit = 16;
      if (Object.keys(updates).length > 0) updateConfig(updates);
    }
  }, [config?.webMode, config?.serpMaxLinksToVisit, config?.webLimit, updateConfig]);

  const renderRightPanel = () => {
    const panelWrapperClass = "w-[45%] pl-4 flex flex-col space-y-6";

    if (config?.webMode === 'Google') {
      return (
        <div className={panelWrapperClass}>
          <SerpSettingsPanel config={config} updateConfig={updateConfig} />
        </div>
      );
    }

    return (
      <div className="w-[45%] pl-4">
        <p className="text-[var(--text)]/70">Select a search mode to see its options.</p>
      </div>
    );
  };

  return (
    <AccordionItem
      value="web-search"
      className={cn(
        "bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md",
        "overflow-hidden",
        "transition-all duration-150 ease-in-out",
        "hover:border-[var(--active)] hover:brightness-105"
      )}
    >
      <AccordionTrigger
        className={cn(
          "flex items-center justify-between w-full px-3 py-2 hover:no-underline",
          "text-[var(--text)] font-medium",
          "hover:brightness-95",
        )}
      >
        <SettingTitle
          icon="🌐"
          text="Web Search"
        />
      </AccordionTrigger>
      <AccordionContent className="px-3 pb-4 pt-2 text-[var(--text)]">
      <div className="flex">
          <WebSearchModeSelector updateConfig={updateConfig} webMode={config?.webMode} />
          {renderRightPanel()}
          </div>
          </AccordionContent>
          </AccordionItem>
  );
};